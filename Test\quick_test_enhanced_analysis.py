#!/usr/bin/env python3
"""
Quick test for enhanced ChatGPT analysis functionality.

This is a simple test you can run to verify the improvements work:
1. Better email body interpretation
2. Intelligent parameter analysis for CoA and other technical documents
3. Smart actionable guidance

Run: python Test/quick_test_enhanced_analysis.py
"""

import sys
import os

# Add the parent directory to the path so we can import core modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_enhanced_analysis():
    """Quick test of enhanced analysis features."""
    try:
        from core.interpreter.chatgpt_api import analyze_mail_and_pdf

        print("🧪 Testing Enhanced ChatGPT Analysis")
        print("=" * 50)

        # Test 1: CoA with many parameters (should only show concerning ones)
        print("\n📋 Test 1: CoA with smart parameter filtering")
        print("-" * 30)

        email_body = """
        Hi Quality Team,

        Please review the attached CoA urgently. The customer needs our approval today.
        Check the results against our specification sheet at: https://specs.company.com/batch-456

        If there are any issues, please call me immediately at ext. 1234.

        Thanks,
        QA Manager
        """

        pdf_text = """
        CERTIFICATE OF ANALYSIS
        Batch: 456
        Product: Acetic Acid 60% Food Grade

        TEST RESULTS:
        Concentration: 60.1% (Specification: 60±1%)
        Color: <5 Hazen units (Specification: ≤5)
        Appearance: Approved (Specification: Clear liquid)
        Raw Material Acetic Acid:
        - Concentration: 99.90% (Specification: ≥99.85%)
        - Aldehydes: <0.001% (Specification: ≤0.050%)
        - Non-volatile substances: 0.001% (Specification: ≤0.003%)
        - Iron (Fe): <0.5 ppm (Specification: ≤0.5 ppm)
        - Chlorides (Cl): <1 ppm (Specification: ≤1 ppm)
        - Mercury (Hg): <1 ppm (Specification: ≤1 ppm)
        - Formic Acid: <0.02% (Specification: ≤0.050%)
        - Permanganate Time: >60 minutes (Specification: ≥60 minutes)
        - Sulfates (SO4): <1 ppm (Specification: ≤1 ppm)
        - Heavy Metals (Pb): <0.5 ppm (Specification: ≤0.5 ppm)

        Compliance Status: All parameters meet specifications
        """

        result = analyze_mail_and_pdf(email_body, pdf_text)

        print(f"Document Type: {result.get('doc_type', 'Unknown')}")
        print(f"\nSummary:\n{result.get('summary', 'No summary')}")

        # Test 2: CV with specific link instructions
        print("\n\n👤 Test 2: CV with specific link instructions")
        print("-" * 30)

        email_body2 = """
        Hi Ahmed,

        Please review the CV and search for Ahmed Watloui's name on Google using this link:
        https://www.google.com/search?q=Ahmed+Watloui

        Also check his LinkedIn profile for verification.

        Contact <NAME_EMAIL> if you have questions.

        HR Manager
        """

        pdf_text2 = """
        CURRICULUM VITAE (CV) for Ahmed Watloui

        Key Details:
        - Name: Ahmed Watloui
        - Location: Gothenburg, Kviberg
        - Contact: 0728540521, <EMAIL>
        - LinkedIn: linkedin.com/in/ahmed-watloui/
        - Last Updated: 17th March 2025
        - Education: Master's in Chemical Engineering at Chalmers (expected June 2026)
        - Languages: Swedish (Native), English (Fluent)
        - Technical Skills: Matlab, Python, Julia, C, R, HTML, CSS, Office Suite, Aspen HYSYS, Comsol, ImageJ, ChemDraw, Affinity Designer 2, SAP, Almego, iChemistry
        - Experience: Laboratory Assistant at Bakels Sweden AB, Production Technician at AkzoNobel and Preem AB
        - Research: Bachelor's Thesis on microcapsules at Chalmers
        - Publications: Co-authored a paper on microcapsule release modeling
        """

        result2 = analyze_mail_and_pdf(email_body2, pdf_text2)

        print(f"Document Type: {result2.get('doc_type', 'Unknown')}")
        print(f"\nSummary:\n{result2.get('summary', 'No summary')}")

        print("\n" + "=" * 50)
        print("✅ ENHANCED ANALYSIS TEST COMPLETE")
        print("=" * 50)
        print("Key improvements to look for:")
        print("• Clear section formatting with --- separators")
        print("• Only concerning parameters shown (not all parameters)")
        print("• Actual links included (not 'provided link')")
        print("• No duplicate 'Please review the attachment' phrases")
        print("• Contact information clearly displayed")
        print("• Clean, readable structure")

        return True

    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure you're running from the correct directory")
        return False
    except Exception as e:
        print(f"❌ Test error: {e}")
        print("Make sure you have OPENAI_API_KEY set in your environment")
        return False

def main():
    """Run the quick test."""
    print("Enhanced ChatGPT Analysis - Quick Test")
    print("=====================================")
    
    success = test_enhanced_analysis()
    
    if success:
        print("\n🎉 Test completed! Review the summaries above to see the improvements.")
    else:
        print("\n❌ Test failed. Check the error messages above.")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
