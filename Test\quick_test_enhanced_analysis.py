#!/usr/bin/env python3
"""
Quick test for enhanced ChatGPT analysis functionality.

This is a simple test you can run to verify the improvements work:
1. Better email body interpretation
2. Intelligent parameter analysis for CoA and other technical documents
3. Smart actionable guidance

Run: python Test/quick_test_enhanced_analysis.py
"""

import sys
import os

# Add the parent directory to the path so we can import core modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_enhanced_analysis():
    """Quick test of enhanced analysis features."""
    try:
        from core.interpreter.chatgpt_api import analyze_mail_and_pdf
        
        print("🧪 Testing Enhanced ChatGPT Analysis")
        print("=" * 50)
        
        # Test 1: CoA with parameters out of spec
        print("\n📋 Test 1: CoA with concerning parameters")
        print("-" * 30)
        
        email_body = """
        Hi Quality Team,
        
        Please review the attached CoA urgently. The customer needs our approval today.
        Check the results against our specification sheet at: https://specs.company.com/batch-456
        
        If there are any issues, please call me immediately at ext. 1234.
        
        Thanks,
        QA Manager
        """
        
        pdf_text = """
        CERTIFICATE OF ANALYSIS
        Batch: 456
        Product: Chemical ABC
        
        TEST RESULTS:
        pH: 6.8 (Specification: 7.0-7.5) ❌ OUT OF SPEC
        Moisture: 1.2% (Specification: ≤1.0%) ❌ OUT OF SPEC  
        Purity: 99.7% (Specification: ≥99.5%) ✅ PASS
        Heavy Metals: 0.2 ppm (Specification: ≤0.5 ppm) ✅ PASS
        
        Overall Status: REJECTED - pH and moisture out of specification
        """
        
        result = analyze_mail_and_pdf(email_body, pdf_text)
        
        print(f"Document Type: {result.get('doc_type', 'Unknown')}")
        print(f"\nSummary:\n{result.get('summary', 'No summary')}")
        
        # Test 2: Invoice (should focus on financial aspects, not parameters)
        print("\n\n💰 Test 2: Invoice (should focus on financial details)")
        print("-" * 30)
        
        email_body2 = """
        Hi Accounts Team,
        
        Please process the attached invoice following our new workflow:
        1. Verify against PO-2024-123
        2. Check budget approval
        3. Process payment for early discount
        
        The vendor offers 2% discount if paid within 10 days.
        
        Procurement
        """
        
        pdf_text2 = """
        INVOICE #INV-2024-789
        Date: January 20, 2024
        Amount: $5,250.00
        PO Reference: PO-2024-123
        
        Payment Terms: 2/10 Net 30
        Due Date: February 19, 2024
        Early Payment Discount: 2% if paid by January 30
        
        Items:
        - Raw Materials: $4,500.00
        - Shipping: $250.00
        - Handling: $500.00
        """
        
        result2 = analyze_mail_and_pdf(email_body2, pdf_text2)
        
        print(f"Document Type: {result2.get('doc_type', 'Unknown')}")
        print(f"\nSummary:\n{result2.get('summary', 'No summary')}")
        
        print("\n" + "=" * 50)
        print("✅ ENHANCED ANALYSIS TEST COMPLETE")
        print("=" * 50)
        print("Key improvements to look for:")
        print("• Email instructions (links, deadlines, workflows) mentioned in summary")
        print("• Parameter analysis for technical documents (CoA)")
        print("• Financial focus for commercial documents (Invoice)")
        print("• Actionable guidance based on document analysis")
        print("• Smart recommendations (urgent review vs optional review)")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure you're running from the correct directory")
        return False
    except Exception as e:
        print(f"❌ Test error: {e}")
        print("Make sure you have OPENAI_API_KEY set in your environment")
        return False

def main():
    """Run the quick test."""
    print("Enhanced ChatGPT Analysis - Quick Test")
    print("=====================================")
    
    success = test_enhanced_analysis()
    
    if success:
        print("\n🎉 Test completed! Review the summaries above to see the improvements.")
    else:
        print("\n❌ Test failed. Check the error messages above.")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
