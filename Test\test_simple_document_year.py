#!/usr/bin/env python3
"""
Simple test for document year extraction.
"""

import sys
import os
import json

# Add the parent directory to the path so we can import core modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_simple_extraction():
    """Test simple document year extraction."""
    try:
        from core.interpreter.chatgpt_api import analyze_mail_and_pdf
        
        print("🧪 Simple Document Year Test")
        print("=" * 40)
        
        email_body = "Please review the attached CoA."
        
        pdf_text = """
        CERTIFICATE OF ANALYSIS
        Product: Acetic Acid
        Batch: 6092526
        Manufacturing Date: 2018-11-08
        Expiry: 2020-10-31
        Company: Solveco AB
        """
        
        print("📤 Sending to ChatGPT...")
        result = analyze_mail_and_pdf(email_body, pdf_text)
        
        print(f"📄 Document Type: {result.get('doc_type', 'Unknown')}")
        
        extracted_fields = result.get('extracted_fields', {})
        print(f"🔑 Extracted Fields:")
        for key, value in extracted_fields.items():
            print(f"  {key}: {value}")
        
        # Check if document_year was extracted
        if 'document_year' in extracted_fields:
            doc_year = extracted_fields['document_year']
            print(f"\n✅ Document year extracted: {doc_year}")
            return True
        else:
            print(f"\n❌ No document_year found in extracted fields")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = test_simple_extraction()
    print(f"\nResult: {'SUCCESS' if success else 'FAILED'}")
