"""
Test script for subscription management system.
Tests subscription limits, usage tracking, and plan upgrades.
"""

import os
import sys
import tempfile
import shutil
import json
from datetime import datetime

# Add the parent directory to the path so we can import core modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.subscription_manager import get_subscription_manager, SubscriptionPlan
from core.mailbox_manager import add_mailbox_to_config


def test_subscription_limits():
    """Test subscription limits and usage tracking"""
    print("🧪 Testing Subscription Management System")
    print("=" * 50)
    
    # Create a temporary tenant for testing
    test_tenant_name = "test_subscription_tenant"
    tenant_dir = f"tenants/{test_tenant_name}"
    
    try:
        # Clean up any existing test data
        if os.path.exists(tenant_dir):
            shutil.rmtree(tenant_dir)
        
        # Create tenant directory
        os.makedirs(tenant_dir, exist_ok=True)
        
        # Create basic config
        config = {
            "tenant_name": test_tenant_name,
            "mailboxes": {},
            "defaults": {
                "storage": {"subfolder_format": "{doc_type}/{document_year}/{company_name}"},
                "notification": {"recipients": []},
                "preferred_language": "English",
                "tracking": {"enabled": True},
                "email_filtering": {"process_external_only": True}
            }
        }
        
        with open(f"{tenant_dir}/config.json", 'w') as f:
            json.dump(config, f, indent=2)
        
        print(f"✅ Created test tenant: {test_tenant_name}")
        
        # Test 1: Initial subscription (should be starter)
        print("\n📋 Test 1: Initial Subscription")
        subscription_manager = get_subscription_manager(test_tenant_name)
        info = subscription_manager.get_subscription_info()
        
        print(f"Plan: {info['plan']}")
        print(f"Mailbox limit: {info['limits']['max_mailboxes']}")
        print(f"Document limit: {info['limits']['max_documents_per_month']}")
        print(f"Current usage: {info['usage']['current_mailboxes']} mailboxes, {info['usage']['documents_this_month']} documents")
        
        assert info['plan'] == 'starter'
        assert info['limits']['max_mailboxes'] == 2
        assert info['limits']['max_documents_per_month'] == 100
        print("✅ Initial subscription test passed")
        
        # Test 2: Add mailboxes up to limit
        print("\n📋 Test 2: Mailbox Limits")
        
        # Add first mailbox
        config = add_mailbox_to_config(
            config, "<EMAIL>", "Test Mailbox 1", True, test_tenant_name
        )
        # Save config after adding mailbox
        with open(f"{tenant_dir}/config.json", 'w') as f:
            json.dump(config, f, indent=2)
        print("✅ Added first mailbox")

        # Add second mailbox
        config = add_mailbox_to_config(
            config, "<EMAIL>", "Test Mailbox 2", True, test_tenant_name
        )
        # Save config after adding mailbox
        with open(f"{tenant_dir}/config.json", 'w') as f:
            json.dump(config, f, indent=2)
        print("✅ Added second mailbox")
        
        # Try to add third mailbox (should fail)
        try:
            config = add_mailbox_to_config(
                config, "<EMAIL>", "Test Mailbox 3", True, test_tenant_name
            )
            print("❌ Third mailbox should have failed!")
            assert False, "Should not be able to add third mailbox on starter plan"
        except ValueError as e:
            print(f"✅ Third mailbox correctly rejected: {e}")
        
        # Test 3: Document processing limits
        print("\n📋 Test 3: Document Processing Limits")
        
        # Process documents up to limit
        for i in range(5):
            can_process = subscription_manager.can_process_document()
            if can_process:
                success = subscription_manager.increment_document_count()
                print(f"✅ Processed document {i+1}: {success}")
            else:
                print(f"🚫 Cannot process document {i+1}: limit reached")
                break
        
        usage = subscription_manager.get_usage_stats()
        print(f"Current document usage: {usage.documents_this_month}")
        
        # Test 4: Plan upgrade
        print("\n📋 Test 4: Plan Upgrade")
        
        success = subscription_manager.upgrade_plan(SubscriptionPlan.BUSINESS)
        print(f"Upgrade to business: {success}")
        
        info = subscription_manager.get_subscription_info()
        print(f"New plan: {info['plan']}")
        print(f"New mailbox limit: {info['limits']['max_mailboxes']}")
        print(f"New document limit: {info['limits']['max_documents_per_month']}")
        
        assert info['plan'] == 'business'
        assert info['limits']['max_mailboxes'] == 10
        assert info['limits']['max_documents_per_month'] == 1000
        print("✅ Plan upgrade test passed")
        
        # Test 5: Can now add more mailboxes
        print("\n📋 Test 5: Post-Upgrade Mailbox Addition")
        
        config = add_mailbox_to_config(
            config, "<EMAIL>", "Test Mailbox 3", True, test_tenant_name
        )
        # Save config after adding mailbox
        with open(f"{tenant_dir}/config.json", 'w') as f:
            json.dump(config, f, indent=2)
        print("✅ Added third mailbox after upgrade")
        
        # Test 6: Usage statistics
        print("\n📋 Test 6: Usage Statistics")

        # Get fresh subscription manager to reload data
        subscription_manager = get_subscription_manager(test_tenant_name)
        info = subscription_manager.get_subscription_info()
        print(f"Mailboxes: {info['usage']['current_mailboxes']}/{info['limits']['max_mailboxes']}")
        print(f"Documents: {info['usage']['documents_this_month']}/{info['limits']['max_documents_per_month']}")
        print(f"Remaining mailboxes: {info['usage']['mailboxes_remaining']}")
        print(f"Remaining documents: {info['usage']['documents_remaining']}")

        assert info['usage']['current_mailboxes'] == 3
        assert info['usage']['mailboxes_remaining'] == 7
        print("✅ Usage statistics test passed")
        
        print("\n🎉 All subscription tests passed!")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # Clean up test data
        if os.path.exists(tenant_dir):
            shutil.rmtree(tenant_dir)
        print(f"\n🧹 Cleaned up test tenant: {test_tenant_name}")


def test_api_endpoints():
    """Test subscription API endpoints"""
    print("\n🌐 Testing API Endpoints")
    print("=" * 30)
    
    import requests
    
    base_url = "http://localhost:5000"
    
    try:
        # Test subscription info endpoint
        response = requests.get(f"{base_url}/api/subscription/info?dev_mode=true")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Subscription info: {data['plan']}")
        else:
            print(f"⚠️ API not running or endpoint failed: {response.status_code}")
            
        # Test usage endpoint
        response = requests.get(f"{base_url}/api/subscription/usage?dev_mode=true")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Usage stats: {data['usage']}")
        else:
            print(f"⚠️ Usage endpoint failed: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("⚠️ Web server not running. Start with: python web_server.py")


if __name__ == "__main__":
    test_subscription_limits()
    test_api_endpoints()
