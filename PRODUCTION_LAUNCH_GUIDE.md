# Mail Auto v1 Complete Production Launch Guide

## 🎯 Current Status & Next Steps

### ✅ Completed Foundation
- ✅ Multi-tenant Azure integration with Key Vault
- ✅ Development environment with personal account
- ✅ Subscription management system with limits (Starter/Business/Enterprise)
- ✅ Customer onboarding flow with Azure consent
- ✅ Web portal with admin/customer interfaces
- ✅ Document processing with subscription limits
- ✅ Mailbox management with plan restrictions

### 🚀 Complete Week-by-Week Production Launch Plan

This guide integrates all Azure setup, Key Vault configuration, development environment, and production deployment into one comprehensive step-by-step process.

## **WEEK 1: Complete Azure Infrastructure & Key Vault Setup**

### Day 1: Azure Resource Groups & Key Vaults (Manual Setup)

#### Step 1: Create Production Resource Group
1. **Go to Azure Portal** → https://portal.azure.com
2. **Click "Resource groups"** → **"Create"**
3. **Fill in details:**
   - **Subscription**: Your Azure subscription
   - **Resource group name**: `mail-auto-prod-rg`
   - **Region**: Choose your preferred region (e.g., West Europe)
4. **Click "Review + create"** → **"Create"**

#### Step 2: Create Production Key Vault
1. **Go to "Key vaults"** → **"Create"**
2. **Basics tab:**
   - **Subscription**: Your Azure subscription
   - **Resource group**: `mail-auto-prod-rg` (select the one you just created)
   - **Key vault name**: `mail-auto-prod-kv` (must be globally unique - add numbers if needed)
   - **Region**: Same as your resource group
   - **Pricing tier**: Standard
3. **Access configuration tab:**
   - **Permission model**: Vault access policy
   - **Access policies**: Leave empty for now
   - **Enable access to**: Check "Azure Resource Manager for template deployment"
4. **Networking tab:**
   - **Connectivity method**: Public endpoint (all networks) for now
   - We'll secure this later in production
5. **Advanced tab:**
   - **Soft delete**: ✅ Enabled (90 days retention)
   - **Purge protection**: ✅ Enabled (prevents permanent deletion)
6. **Tags tab (recommended):**
   ```
   Environment: Production
   Project: MailAuto
   Purpose: EmailAutomationCredentials
   Owner: [Your Name]
   ```
7. **Click "Review + create"** → **"Create"**

#### Step 3: Create Development Key Vault
1. **Repeat Step 2** but with these changes:
   - **Key vault name**: `mail-auto-dev-kv`
   - **Tags**: `Environment: Development`
   - **Purge protection**: Can be disabled for development

### Day 2: Multi-Tenant App Registration (Manual Setup)

#### Step 4: Create Multi-Tenant App Registration
1. **Go to "Azure Active Directory"** → **"App registrations"** → **"New registration"**
2. **Fill in details:**
   - **Name**: `Mail Auto Production`
   - **Supported account types**: **"Accounts in any organizational directory (Any Azure AD directory - Multitenant)"**
   - **Redirect URI**:
     - **Type**: Web
     - **URL**: `https://yourdomain.com/auth/callback` (replace with your actual domain)
3. **Click "Register"**

#### Step 5: Configure API Permissions for Full Access
1. **In your new app registration** → **"API permissions"**
2. **Click "Add a permission"** → **"Microsoft Graph"** → **"Application permissions"**
3. **Add these permissions for FULL mailbox and OneDrive access:**
   - `Mail.ReadWrite` - Read and write mail in all mailboxes
   - `Mail.Send` - Send mail as any user
   - `Files.ReadWrite.All` - Full access to all files (OneDrive/SharePoint)
   - `Files.Read.All` - Read all files (backup permission)
4. **Click "Add permissions"**
5. **⚠️ IMPORTANT: Click "Grant admin consent for [Your Tenant]"** → **"Yes"**
   - This gives your app permission to access ANY customer's mailboxes and OneDrive when they consent

**What customers get when they click your onboarding link:**
- ✅ **Full mailbox access** - Read all emails in mailboxes they specify
- ✅ **OneDrive access** - Read and save files to their OneDrive Business
- ✅ **Send emails** - Send notifications FROM their mailboxes (like <EMAIL>)
- ✅ **Create folders** - Organize processed documents automatically
- ✅ **Customer control** - They choose which mailboxes to monitor and can revoke access anytime

#### Step 6: Create Client Secret
1. **Go to "Certificates & secrets"** → **"Client secrets"** → **"New client secret"**
2. **Description**: `Production Secret`
3. **Expires**: 24 months (recommended)
4. **Click "Add"**
5. **⚠️ CRITICAL: Copy the secret value immediately** - you won't see it again!

#### Step 7: Record Essential Information
**Write down these values** (you'll need them throughout setup):
- **Application (client) ID**: Found on the app overview page
- **Directory (tenant) ID**: Found on the app overview page
- **Client secret**: The value you just copied
- **Key Vault URLs**:
  - Production: `https://mail-auto-prod-kv.vault.azure.net/`
  - Development: `https://mail-auto-dev-kv.vault.azure.net/`

### Day 3: Key Vault Security & Access (Manual Setup)

#### Step 8: Configure Key Vault Access Policies
1. **Go to your Production Key Vault** → **"Access policies"** → **"Add Access Policy"**
2. **Configure permissions:**
   - **Configure from template**: Secret Management
   - **Secret permissions**: Get, List, Set, Delete
3. **Select principal:**
   - **Search for**: Your app registration name (`Mail Auto Production`)
   - **Select it** and click **"Select"**
4. **Click "Add"** → **"Save"**
5. **Repeat for Development Key Vault**

#### Step 9: Test Key Vault Connection
1. **Install required packages** if not already done:
```bash
pip install azure-keyvault-secrets azure-identity
```
2. **Create a test file** called `test_keyvault.py`:
```python
from azure.keyvault.secrets import SecretClient
from azure.identity import ClientSecretCredential

# Your values from Step 7
TENANT_ID = "your-tenant-id-here"
CLIENT_ID = "your-client-id-here"
CLIENT_SECRET = "your-client-secret-here"
VAULT_URL = "https://mail-auto-prod-kv.vault.azure.net/"

# Test connection
credential = ClientSecretCredential(TENANT_ID, CLIENT_ID, CLIENT_SECRET)
client = SecretClient(vault_url=VAULT_URL, credential=credential)

# Try to set and get a test secret
try:
    client.set_secret("test-secret", "test-value")
    secret = client.get_secret("test-secret")
    print(f"✅ Key Vault connection successful! Secret value: {secret.value}")
    client.begin_delete_secret("test-secret")  # Clean up
    print("✅ Test secret deleted")
except Exception as e:
    print(f"❌ Key Vault connection failed: {e}")
```
3. **Run the test**: `python test_keyvault.py`
4. **You should see**: "✅ Key Vault connection successful!"

### Day 4: Environment Configuration & Security (Manual Setup)

#### Step 10: Create Secure Environment Configuration
1. **Create a file** called `.env` in your project root:
```bash
# Environment Configuration
MAIL_AUTO_ENVIRONMENT=production

# Production Key Vault Settings
PROD_KEY_VAULT_URL=https://mail-auto-prod-kv.vault.azure.net/
PROD_TENANT_ID=your-tenant-id-here
PROD_CLIENT_ID=your-client-id-here
PROD_CLIENT_SECRET=your-client-secret-here
PROD_USE_KEY_VAULT=true
PROD_USE_MANAGED_IDENTITY=false

# Development Key Vault Settings
DEV_KEY_VAULT_URL=https://mail-auto-dev-kv.vault.azure.net/
DEV_TENANT_ID=your-tenant-id-here
DEV_CLIENT_ID=your-client-id-here
DEV_CLIENT_SECRET=your-client-secret-here
DEV_USE_KEY_VAULT=true
DEV_USE_MANAGED_IDENTITY=false

# OpenAI API Configuration
OPENAI_API_KEY=your-openai-api-key-here

# Optional: Logging and Debug Settings
LOG_LEVEL=INFO
DEBUG_MODE=false
```

2. **⚠️ SECURITY: Add .env to .gitignore**
```bash
echo ".env" >> .gitignore
```

#### Step 11: Configure Network Security (Manual Setup)
1. **Go to Production Key Vault** → **"Networking"**
2. **For production security:**
   - **Public network access**: Selected networks
   - **Add your current IP** to allowed IP addresses
   - **Check**: "Allow trusted Microsoft services to bypass this firewall"
3. **For development** (keep it simple for now):
   - **Public network access**: All networks

#### Step 12: Enable Monitoring & Logging (Manual Setup)
1. **Go to Key Vault** → **"Diagnostic settings"** → **"Add diagnostic setting"**
2. **Configure:**
   - **Name**: `mail-auto-kv-logs`
   - **Logs**: ✅ AuditEvent
   - **Metrics**: ✅ AllMetrics
   - **Destination**: Send to Log Analytics workspace (create one if needed)
3. **Save**

### Day 5: Data Migration & Testing

#### Step 13: Migrate Existing Data to Key Vault
1. **If you have existing tenant data** in local files:
```bash
python migrate_to_keyvault.py --backup
```
2. **This will:**
   - Backup your existing files
   - Move credentials to Key Vault
   - Keep your tenant configurations working

#### Step 14: Test Environment Switching
1. **Test development mode:**
```bash
# Set environment to development
export MAIL_AUTO_ENVIRONMENT=development  # Linux/Mac
set MAIL_AUTO_ENVIRONMENT=development     # Windows

# Test your system
python main.py --once
```

2. **Test production mode:**
```bash
# Set environment to production
export MAIL_AUTO_ENVIRONMENT=production   # Linux/Mac
set MAIL_AUTO_ENVIRONMENT=production      # Windows

# Test your system
python main.py --once
```

#### Step 15: Verify Complete Setup
1. **Run comprehensive test:**
```python
# Create verify_setup.py
from core.config import config_manager
from core.key_vault_service import key_vault_service

print(f"Environment: {config_manager.current_environment}")
print(f"Key Vault URL: {config_manager.config.key_vault.vault_url}")
print(f"Using Key Vault: {config_manager.config.use_key_vault}")

# Test Key Vault connection
try:
    tenants = key_vault_service.list_tenant_secrets()
    print(f"✅ Found {len(tenants)} tenants in Key Vault")
except Exception as e:
    print(f"❌ Key Vault connection failed: {e}")
```

2. **Run verification:**
```bash
python verify_setup.py
```

**✅ Week 1 Success Criteria:**
- ✅ Azure infrastructure created without errors
- ✅ Key Vault connection working in both environments
- ✅ App registration configured with proper permissions
- ✅ Environment switching works correctly
- ✅ Existing data migrated to Key Vault (if applicable)

## **WEEK 2: Customer Registration & Development Environment Setup**

### Day 1-2: Development Environment Setup

#### Step 16: Install Development Dependencies
1. **Install frontend dependencies:**
```bash
cd core/web_portal
npm install
cd ../..
```

2. **Install Python dependencies** (if not already done):
```bash
pip install -r requirements.txt
```

#### Step 17: Start Development Environment
1. **Start both frontend and backend servers:**
```bash
python start_dev_server.py
```

This will start:
- **Flask Backend**: http://localhost:5000 (API endpoints)
- **Vite Frontend**: http://localhost:5173 (Web interface)

2. **Access the Application:**
- **Main Application**: http://localhost:5173
- **API Health Check**: http://localhost:5000/api/health
- **Backend Direct**: http://localhost:5000

#### Step 18: Test Development Features
**Development Features Available:**
- **Hot Reload**: Frontend automatically reloads on file changes (Vite)
- **API Proxy**: Frontend automatically proxies `/api/*` calls to Flask backend
- **Environment Indicator**: Development indicator in top-right corner
- **Live Data**: Real-time data from your Mail_Auto system

### Day 3-4: Customer Registration System

#### Step 19: Build Customer Registration Page
1. **Create a simple web form** where customers enter:
   - Company name
   - Email address
   - Choose subscription plan (Starter/Business/Enterprise)

2. **Add registration form to your web portal** or create a simple HTML page

#### Step 20: Build Registration Backend
```python
# Add to web_server.py
@app.route('/api/register', methods=['POST'])
def register_customer():
    data = request.get_json()

    # Get customer info
    company_name = data.get('company_name')
    email = data.get('email')
    plan = data.get('subscription_plan', 'starter')

    # Create tenant name (safe for folders)
    tenant_name = company_name.lower().replace(' ', '-').replace('.', '')

    # Generate onboarding link
    onboarding_url = f"https://yourdomain.com/onboard/{tenant_name}"

    # Store customer registration (you'll add database later)
    # For now, just return the onboarding link

    return jsonify({
        'success': True,
        'onboarding_url': onboarding_url,
        'message': f'Please complete setup at: {onboarding_url}'
    })
```

#### Step 21: Customer Onboarding Flow Setup
1. **Update your tenant onboarding service** to handle the registration flow
2. **Test the onboarding URL generation**
3. **Ensure the onboarding service is running:**
```bash
python core/tenant_onboarding.py
```

### Day 5: Customer Onboarding Flow Testing

#### Step 22: What Happens When Customer Clicks Onboarding Link

**Customer Journey (Simple Explanation):**
1. **Customer clicks link** → `https://yourdomain.com/onboard/acme-corp`
2. **Your system redirects them to Microsoft** → "ACME Corp wants to access your emails"
3. **Customer sees permission screen** → Lists exactly what you can access
4. **Customer clicks "Accept"** → Microsoft sends them back to your app
5. **Your app gets access token** → Can now read their emails
6. **You store their credentials** → Safely in Azure Key Vault
7. **Customer gets dashboard access** → Can configure mailboxes and document types

#### Step 10: Configure Customer Mailbox Selection
When customer completes onboarding, they see a setup page:

```
📧 Which mailboxes should Mail Auto monitor?
☑️ <EMAIL>
☑️ <EMAIL>
☑️ <EMAIL>
☐ <EMAIL>
☐ <EMAIL>

📄 Which document types should we process?
☑️ Invoices → Save to: Invoices/{year}/{company}
☑️ Contracts → Save to: Contracts/{year}/{company}
☑️ Certificates → Save to: Certificates/{year}/{company}
☐ Purchase Orders
☐ Legal Documents

💾 Where should we save processed documents?
○ OneDrive Business
○ SharePoint
○ Local folders
```

### Day 5: Customer Authentication System

#### Step 11: Replace dev_mode with Real Customer Auth
```python
# Update web_server.py endpoints to use customer authentication
from core.customer_auth import get_customer_tenant_name

@app.route('/api/subscription/info')
def get_subscription_info():
    # Get customer from JWT token instead of dev_mode
    tenant_name = get_customer_tenant_name(request)
    if not tenant_name:
        return jsonify({'error': 'Please log in'}), 401

    # Rest of your code stays the same
    subscription_manager = get_subscription_manager(tenant_name)
    return jsonify(subscription_manager.get_subscription_info())
```

## **🚨 CRITICAL: Production Server Architecture**

### **Current Development vs Production Server Setup**

#### **Current Architecture Understanding:**
- **`core/tenant_onboarding.py`**: Standalone Flask app (port 8000) for customer onboarding
- **`core/web_portal/`**: React/Vite frontend (port 5173 in dev)
- **`web_server.py`**: Main Flask backend (port 5000) serving API and admin interface

#### **⚠️ WSGI Production Requirement**
The warning you see: `"This is a development server. Do not use it in production deployment. Use a production WSGI server instead."` is **CRITICAL** for production.

**Why This Matters:**
- **Customer onboarding links** (`/onboard/<customer_name>`) trigger the Flask development server
- **Development server** is single-threaded and not secure for production
- **Multiple customers** clicking onboarding links simultaneously will cause failures
- **Security vulnerabilities** exist in development server

### **🏗️ Production Server Integration Strategy**

#### **Option 1: Unified Server Architecture (RECOMMENDED)**
**Integrate everything into one production-ready server:**

1. **Merge `tenant_onboarding.py` routes into `web_server.py`**
2. **Use Gunicorn/uWSGI** for production WSGI serving
3. **Single domain** handles everything: `https://yourdomain.com`
4. **Routes structure:**
   ```
   https://yourdomain.com/                    → Customer dashboard
   https://yourdomain.com/admin               → Admin interface
   https://yourdomain.com/onboard/<customer>  → Customer onboarding
   https://yourdomain.com/auth/callback       → OAuth callback
   https://yourdomain.com/api/*               → API endpoints
   ```

#### **Option 2: Microservices Architecture**
**Keep separate services but make them production-ready:**

1. **Onboarding Service**: Gunicorn + `tenant_onboarding.py` (port 8000)
2. **Main Application**: Gunicorn + `web_server.py` (port 5000)
3. **Load Balancer**: Nginx routing between services
4. **Shared Database**: For tenant state management

### **🎯 Recommended Implementation Plan**

#### **Step 1: Integrate Onboarding into Main Server**
**Merge `tenant_onboarding.py` routes into `web_server.py`:**

```python
# Add to web_server.py
from core.tenant_onboarding import TenantOnboardingService

# Initialize onboarding service
config = config_manager.get_environment_config("production")
onboarding_service = TenantOnboardingService(
    client_id=config.key_vault.client_id,
    client_secret=config.key_vault.client_secret,
    redirect_uri=config.redirect_uri
)

@app.route("/onboard/<customer_name>")
def onboard_customer(customer_name: str):
    """Customer onboarding endpoint - integrated into main server"""
    consent_url, state = onboarding_service.generate_consent_url(customer_name)
    return redirect(consent_url)

@app.route("/auth/callback")
def auth_callback():
    """OAuth callback endpoint - integrated into main server"""
    # Handle OAuth callback logic here
    # (Move from tenant_onboarding.py)
```

#### **Step 2: Production WSGI Configuration**
**Create `gunicorn.conf.py`:**

```python
# gunicorn.conf.py
bind = "0.0.0.0:5000"
workers = 4
worker_class = "sync"
worker_connections = 1000
timeout = 30
keepalive = 2
max_requests = 1000
max_requests_jitter = 100
preload_app = True
```

#### **Step 3: Production Startup Script**
**Create `start_production.py`:**

```python
#!/usr/bin/env python3
"""
Production startup script for Mail Auto
Uses Gunicorn WSGI server for production deployment
"""
import os
import subprocess
import sys

def start_production_server():
    """Start production server with Gunicorn"""

    # Set production environment
    os.environ['MAIL_AUTO_ENVIRONMENT'] = 'production'
    os.environ['FLASK_ENV'] = 'production'

    # Build frontend if needed
    print("Building frontend for production...")
    subprocess.run(["npm", "run", "build"], cwd="core/web_portal", check=True)

    # Start Gunicorn server
    print("Starting production server with Gunicorn...")
    cmd = [
        "gunicorn",
        "--config", "gunicorn.conf.py",
        "web_server:app"
    ]

    subprocess.run(cmd, check=True)

if __name__ == "__main__":
    start_production_server()
```

#### **Step 4: Frontend Production Build Integration**
**Update `web_server.py` to serve built frontend:**

```python
# Add to web_server.py
@app.route('/')
@app.route('/<path:path>')
def serve_frontend(path=''):
    """Serve React frontend in production"""
    if app.config.get('ENV') == 'production':
        # Serve built React app
        if path and os.path.exists(os.path.join('core/web_portal/dist', path)):
            return send_from_directory('core/web_portal/dist', path)
        else:
            return send_from_directory('core/web_portal/dist', 'index.html')
    else:
        # Development mode - proxy to Vite
        return jsonify({"message": "Development mode - use Vite server on port 5173"})
```

### **🔧 Code Integration Instructions**

#### **Phase 1: Merge Onboarding Service (Week 3, Day 1)**
1. **Copy onboarding routes** from `tenant_onboarding.py` to `web_server.py`
2. **Import TenantOnboardingService** class into `web_server.py`
3. **Test onboarding flow** through main server
4. **Remove standalone** `tenant_onboarding.py` server

#### **Phase 2: Production WSGI Setup (Week 3, Day 2)**
1. **Install Gunicorn**: `pip install gunicorn`
2. **Create configuration files** (`gunicorn.conf.py`, `start_production.py`)
3. **Test production server** locally
4. **Verify all routes work** through Gunicorn

#### **Phase 3: Frontend Integration (Week 3, Day 3)**
1. **Build React app**: `npm run build` in `core/web_portal/`
2. **Configure Flask** to serve built files
3. **Test complete application** in production mode
4. **Verify customer onboarding** works end-to-end

#### **Phase 4: Azure Deployment (Week 3, Day 4-5)**
1. **Deploy to Azure App Service** or **Azure Container Instances**
2. **Configure environment variables** in Azure
3. **Set up custom domain** and SSL certificates
4. **Test customer onboarding** with real Azure AD

### **🎯 Success Criteria for Production Server**
- ✅ **Single domain** handles all routes
- ✅ **Gunicorn WSGI** server running (no development server warnings)
- ✅ **Customer onboarding links** work under load
- ✅ **Frontend and backend** integrated seamlessly
- ✅ **Environment variables** properly configured
- ✅ **SSL certificates** and custom domain working

## **WEEK 3: Customer Dashboard & Real Data Integration**

### Day 1: Integrate Onboarding Service into Main Web Server

#### Step 23: Merge Tenant Onboarding into Web Server
**Goal**: Eliminate the standalone Flask development server warning by integrating everything into the main production-ready server.

1. **Open `web_server.py`** and add the onboarding imports:
```python
# Add these imports to web_server.py
from core.tenant_onboarding import TenantOnboardingService
from flask import redirect
```

2. **Initialize the onboarding service** in `web_server.py`:
```python
# Add after other service initializations in web_server.py
def get_onboarding_service():
    """Get onboarding service with current environment config"""
    config = config_manager.get_environment_config("production")
    return TenantOnboardingService(
        client_id=config.key_vault.client_id,
        client_secret=config.key_vault.client_secret,
        redirect_uri=config.redirect_uri
    )

onboarding_service = get_onboarding_service()
```

3. **Add onboarding routes** to `web_server.py`:
```python
# Add these routes to web_server.py

@app.route("/onboard/<customer_name>")
def onboard_customer(customer_name: str):
    """Customer onboarding endpoint - now integrated into main server"""
    try:
        consent_url, state = onboarding_service.generate_consent_url(customer_name)

        # Store state for validation (in production, use secure storage like Redis)
        # For now, we'll rely on Azure AD's built-in state validation

        return redirect(consent_url)
    except Exception as e:
        return jsonify({"error": f"Failed to generate consent URL: {str(e)}"}), 500

@app.route("/auth/callback")
def auth_callback():
    """Handle OAuth callback from Azure AD"""
    code = request.args.get('code')
    state = request.args.get('state')
    tenant = request.args.get('tenant')
    error = request.args.get('error')

    if error:
        return jsonify({"error": error, "description": request.args.get('error_description')}), 400

    if not code or not state or not tenant:
        return jsonify({"error": "Missing required parameters"}), 400

    result = onboarding_service.handle_consent_callback(code, state, tenant)

    if result["success"]:
        # Redirect to customer dashboard after successful onboarding
        customer_name = result['tenant_name']
        return f"""
        <html>
        <head>
            <title>Mail Auto - Successfully Connected!</title>
            <style>
                body {{ font-family: Arial, sans-serif; text-align: center; padding: 50px; }}
                .success {{ color: #28a745; }}
                .info {{ color: #6c757d; margin: 20px 0; }}
                .button {{
                    background-color: #007bff;
                    color: white;
                    padding: 10px 20px;
                    text-decoration: none;
                    border-radius: 5px;
                    display: inline-block;
                    margin-top: 20px;
                }}
            </style>
        </head>
        <body>
            <h1 class="success">🎉 Successfully Connected!</h1>
            <p>Welcome <strong>{result['tenant_name']}</strong>! Your tenant is now connected to Mail Auto.</p>
            <div class="info">
                <p>Tenant ID: {result['tenant_id']}</p>
                <p>Subscription Plan: {result['subscription']['plan']}</p>
                <p>Mailboxes Available: {result['subscription']['limits']['max_mailboxes']}</p>
                <p>Monthly Documents: {result['subscription']['limits']['max_documents_per_month']}</p>
            </div>
            <a href="/" class="button">Go to Dashboard</a>
            <p><small>You can now close this window or click above to access your dashboard.</small></p>
        </body>
        </html>
        """
    else:
        return f"""
        <html>
        <head>
            <title>Mail Auto - Connection Failed</title>
            <style>
                body {{ font-family: Arial, sans-serif; text-align: center; padding: 50px; }}
                .error {{ color: #dc3545; }}
            </style>
        </head>
        <body>
            <h1 class="error">❌ Connection Failed</h1>
            <p>Error: {result['error']}</p>
            <p>Please contact support if this issue persists.</p>
            <a href="mailto:<EMAIL>">Contact Support</a>
        </body>
        </html>
        """
```

4. **Test the integration**:
```bash
# Stop any running tenant_onboarding.py servers
# Start only the main web server
python web_server.py

# Test onboarding URL (replace 'test-customer' with actual customer name)
# Visit: http://localhost:5000/onboard/test-customer
```

#### Step 24: Install and Configure Gunicorn for Production
1. **Install Gunicorn**:
```bash
pip install gunicorn
```

2. **Create `gunicorn.conf.py`** in your project root:
```python
# gunicorn.conf.py
import multiprocessing

# Server socket
bind = "0.0.0.0:5000"
backlog = 2048

# Worker processes
workers = multiprocessing.cpu_count() * 2 + 1
worker_class = "sync"
worker_connections = 1000
timeout = 30
keepalive = 2

# Restart workers after this many requests, to help prevent memory leaks
max_requests = 1000
max_requests_jitter = 100

# Load application code before the worker processes are forked
preload_app = True

# Logging
accesslog = "-"
errorlog = "-"
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'

# Process naming
proc_name = "mail_auto_production"

# Server mechanics
daemon = False
pidfile = "/tmp/mail_auto.pid"
user = None
group = None
tmp_upload_dir = None

# SSL (configure if using HTTPS directly through Gunicorn)
# keyfile = "/path/to/keyfile"
# certfile = "/path/to/certfile"
```

3. **Create `start_production.py`** in your project root:
```python
#!/usr/bin/env python3
"""
Production startup script for Mail Auto
Uses Gunicorn WSGI server for production deployment
"""
import os
import subprocess
import sys
from pathlib import Path

def start_production_server():
    """Start production server with Gunicorn"""

    # Set production environment
    os.environ['MAIL_AUTO_ENVIRONMENT'] = 'production'
    os.environ['FLASK_ENV'] = 'production'

    # Ensure we're in the right directory
    project_root = Path(__file__).parent
    os.chdir(project_root)

    # Build frontend for production
    print("🏗️  Building frontend for production...")
    try:
        result = subprocess.run(
            ["npm", "run", "build"],
            cwd="core/web_portal",
            check=True,
            capture_output=True,
            text=True
        )
        print("✅ Frontend build completed successfully")
    except subprocess.CalledProcessError as e:
        print(f"❌ Frontend build failed: {e}")
        print(f"stdout: {e.stdout}")
        print(f"stderr: {e.stderr}")
        sys.exit(1)

    # Start Gunicorn server
    print("🚀 Starting production server with Gunicorn...")
    print("📍 Server will be available at: http://localhost:5000")
    print("🔗 Customer onboarding: http://localhost:5000/onboard/<customer-name>")
    print("⚙️  Admin interface: http://localhost:5000/admin")

    try:
        cmd = [
            "gunicorn",
            "--config", "gunicorn.conf.py",
            "web_server:app"
        ]

        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to start Gunicorn: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")

if __name__ == "__main__":
    start_production_server()
```

4. **Test production server**:
```bash
# Start production server
python start_production.py

# You should see:
# ✅ Frontend build completed successfully
# 🚀 Starting production server with Gunicorn...
# [INFO] Starting gunicorn 20.1.0
# [INFO] Listening at: http://0.0.0.0:5000
# [INFO] Using worker: sync
# [INFO] Booting worker with pid: [PID]

# No more development server warnings!
```

### Day 2: Frontend Production Integration

#### Step 25: Configure Web Server to Serve Built Frontend
1. **Update `web_server.py`** to serve the built React app:
```python
# Add this route to web_server.py (replace any existing root route)

@app.route('/')
@app.route('/<path:path>')
def serve_frontend(path=''):
    """Serve React frontend in production, proxy to Vite in development"""

    # Check if we're in production mode
    if os.environ.get('FLASK_ENV') == 'production':
        # Production: serve built React app
        dist_dir = 'core/web_portal/dist'

        if path and os.path.exists(os.path.join(dist_dir, path)):
            return send_from_directory(dist_dir, path)
        else:
            # Serve index.html for all routes (React Router will handle routing)
            return send_from_directory(dist_dir, 'index.html')
    else:
        # Development mode: return info about Vite server
        return jsonify({
            "message": "Development mode active",
            "frontend": "http://localhost:5173",
            "backend": "http://localhost:5000",
            "note": "Use the Vite dev server for frontend development"
        })
```

2. **Update the React app's API configuration** to work in production:
```javascript
// In core/web_portal/src/services/api.js (or wherever you configure API calls)

const API_BASE_URL = process.env.NODE_ENV === 'production'
    ? '' // Same domain in production
    : 'http://localhost:5000'; // Separate port in development

// Update all API calls to use this base URL
export const api = {
    get: (endpoint) => fetch(`${API_BASE_URL}/api${endpoint}`),
    post: (endpoint, data) => fetch(`${API_BASE_URL}/api${endpoint}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
    }),
    // ... other methods
};
```

### Day 3: Complete Integration Testing

#### Step 26: Test Complete Production Flow
1. **Build and start production server**:
```bash
python start_production.py
```

2. **Test all endpoints**:
```bash
# Test main application
curl http://localhost:5000/

# Test API health
curl http://localhost:5000/api/health

# Test admin interface
curl http://localhost:5000/admin

# Test customer onboarding (replace with real customer name)
curl -I http://localhost:5000/onboard/test-customer
# Should return 302 redirect to Microsoft login
```

3. **Verify no development server warnings**:
   - ✅ No "This is a development server" warnings
   - ✅ Multiple concurrent requests work
   - ✅ Customer onboarding links work under load

#### Step 27: Environment Configuration for Production
1. **Update `.env` file** for production:
```bash
# Production Environment Settings
MAIL_AUTO_ENVIRONMENT=production
FLASK_ENV=production

# Production URLs (update with your actual domain)
PROD_REDIRECT_URI=https://yourdomain.com/auth/callback
PROD_BASE_URL=https://yourdomain.com

# Key Vault settings (from Week 1)
PROD_KEY_VAULT_URL=https://mail-auto-prod-kv.vault.azure.net/
PROD_TENANT_ID=your-tenant-id-here
PROD_CLIENT_ID=your-client-id-here
PROD_CLIENT_SECRET=your-client-secret-here
PROD_USE_KEY_VAULT=true

# OpenAI API
OPENAI_API_KEY=your-openai-api-key-here
```

2. **Test environment switching**:
```bash
# Test development mode
export MAIL_AUTO_ENVIRONMENT=development
python web_server.py
# Should show development indicators

# Test production mode
export MAIL_AUTO_ENVIRONMENT=production
python start_production.py
# Should use production Key Vault and settings
```

### Day 4-5: Customer Dashboard Real Data

#### Step 28: Show Real Customer Data (Not Placeholders)
```javascript
// Update web_portal/script.js to show real tenant data
async function loadCustomerDashboard() {
    // Get customer's JWT token from login
    const token = localStorage.getItem('customer_token');

    // Fetch real subscription info
    const subResponse = await fetch('/api/subscription/info', {
        headers: { 'Authorization': `Bearer ${token}` }
    });
    const subscription = await subResponse.json();

    // Show real plan and usage
    document.getElementById('current-plan').textContent = subscription.plan;
    document.getElementById('mailboxes-used').textContent =
        `${subscription.usage.current_mailboxes}/${subscription.limits.max_mailboxes}`;
    document.getElementById('documents-used').textContent =
        `${subscription.usage.documents_this_month}/${subscription.limits.max_documents_per_month}`;
}
```

#### Step 13: Customer Mailbox Configuration
```python
# Add endpoint for customer to configure their mailboxes
@app.route('/api/customer/mailboxes', methods=['GET', 'POST'])
def manage_customer_mailboxes():
    tenant_name = get_customer_tenant_name(request)
    if not tenant_name:
        return jsonify({'error': 'Authentication required'}), 401

    if request.method == 'GET':
        # Return customer's current mailbox configuration
        config = load_tenant_config(tenant_name)
        return jsonify(config.get('mailboxes', {}))

    elif request.method == 'POST':
        # Add new mailbox for customer
        data = request.get_json()
        mailbox_email = data.get('mailbox_email')

        # Check subscription limits
        subscription_manager = get_subscription_manager(tenant_name)
        if not subscription_manager.can_add_mailbox():
            return jsonify({'error': 'Mailbox limit reached. Please upgrade your plan.'}), 400

        # Add mailbox to customer's config
        # Your existing mailbox_manager code handles this
        return jsonify({'success': True})
```

### Day 3-4: Document Type Configuration

#### Step 14: Customer Document Type Setup
```python
# Let customers configure which document types to process
@app.route('/api/customer/document-types', methods=['GET', 'POST'])
def manage_document_types():
    tenant_name = get_customer_tenant_name(request)

    if request.method == 'GET':
        # Return available document types and customer's settings
        config = load_tenant_config(tenant_name)
        return jsonify({
            'available_types': ['invoices', 'contracts', 'certificates', 'purchase_orders'],
            'customer_settings': config.get('document_types', {})
        })

    elif request.method == 'POST':
        # Customer enables/disables document types
        data = request.get_json()
        doc_type = data.get('document_type')
        enabled = data.get('enabled', True)
        storage_path = data.get('storage_path', f'{doc_type}/{{year}}/{{company}}')

        # Update customer's config
        # If customer doesn't specify settings, use global defaults
        return jsonify({'success': True})
```

### Day 5: Test Customer Experience

#### Step 15: End-to-End Customer Test
1. **Register new test customer** → Use your registration form
2. **Complete onboarding** → Click onboarding link, grant permissions
3. **Configure mailboxes** → Select which mailboxes to monitor
4. **Set document types** → Choose what documents to process
5. **Test email processing** → Send test email, verify it gets processed
6. **Check subscription limits** → Try to exceed limits, verify blocking works

## **WEEK 4: Billing Integration & Production Deployment**

### Day 1-2: Stripe Billing Integration

#### Step 16: Setup Stripe Account
1. **Create Stripe account** → Go to stripe.com, sign up
2. **Get API keys** → Dashboard → Developers → API keys
3. **Create products** → Dashboard → Products → Add products for each plan

```python
# Add to your .env file
STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...
```

#### Step 17: Create Subscription Plans in Stripe
```python
# Run this once to create your plans in Stripe
import stripe
stripe.api_key = "sk_test_..."

# Create Starter Plan
starter_price = stripe.Price.create(
    unit_amount=2900,  # €29.00
    currency='eur',
    recurring={'interval': 'month'},
    product_data={'name': 'Mail Auto Starter Plan'}
)

# Create Business Plan
business_price = stripe.Price.create(
    unit_amount=9900,  # €99.00
    currency='eur',
    recurring={'interval': 'month'},
    product_data={'name': 'Mail Auto Business Plan'}
)

# Create Enterprise Plan
enterprise_price = stripe.Price.create(
    unit_amount=29900,  # €299.00
    currency='eur',
    recurring={'interval': 'month'},
    product_data={'name': 'Mail Auto Enterprise Plan'}
)
```

#### Step 18: Add Payment to Registration
```python
# Update registration to include payment
@app.route('/api/register-with-payment', methods=['POST'])
def register_with_payment():
    data = request.get_json()

    # Create Stripe customer
    customer = stripe.Customer.create(
        email=data['email'],
        name=data['company_name']
    )

    # Create subscription
    subscription = stripe.Subscription.create(
        customer=customer.id,
        items=[{'price': PLAN_PRICE_IDS[data['plan']]}],
        payment_behavior='default_incomplete',
        expand=['latest_invoice.payment_intent']
    )

    # Return client secret for payment
    return jsonify({
        'client_secret': subscription.latest_invoice.payment_intent.client_secret,
        'subscription_id': subscription.id
    })
```

### Day 3-4: Azure App Service Deployment

#### Step 19: Deploy to Azure App Service (Like putting your app on the internet)
```bash
# Create App Service Plan (like renting a server)
az appservice plan create \
  --name "mail-auto-plan" \
  --resource-group "mail-auto-prod-rg" \
  --sku B1 \
  --is-linux

# Create Web App (your website)
az webapp create \
  --resource-group "mail-auto-prod-rg" \
  --plan "mail-auto-plan" \
  --name "mail-auto-prod" \
  --runtime "PYTHON|3.11"
```

#### Step 20: Configure App Settings
```bash
# Set environment variables for your app
az webapp config appsettings set \
  --resource-group "mail-auto-prod-rg" \
  --name "mail-auto-prod" \
  --settings \
    MAIL_AUTO_ENVIRONMENT=production \
    PROD_KEY_VAULT_URL=https://mail-auto-prod-vault.vault.azure.net/ \
    STRIPE_SECRET_KEY=your_stripe_secret_key
```

### Day 5: Domain & SSL Setup

#### Step 21: Add Your Domain
```bash
# Add custom domain (replace yourdomain.com with your actual domain)
az webapp config hostname add \
  --webapp-name "mail-auto-prod" \
  --resource-group "mail-auto-prod-rg" \
  --hostname "yourdomain.com"

# Enable HTTPS (secure connection)
az webapp config ssl bind \
  --name "mail-auto-prod" \
  --resource-group "mail-auto-prod-rg" \
  --ssl-type SNI \
  --certificate-thumbprint YOUR_CERT_THUMBPRINT
```

## **WEEK 5: Beta Testing & Launch**

### Day 1-2: Beta Customer Onboarding

#### Step 22: Invite 5-10 Beta Customers
1. **Choose friendly customers** → People who won't mind if something breaks
2. **Send them registration link** → `https://yourdomain.com/register`
3. **Guide them through onboarding** → Be available to help
4. **Monitor their usage** → Watch for errors or issues

#### Step 23: Monitor Beta Customer Activity
```python
# Add monitoring dashboard for beta customers
@app.route('/admin/beta-monitoring')
def beta_monitoring():
    """Monitor beta customer activity"""
    beta_customers = get_beta_customers()

    stats = []
    for customer in beta_customers:
        tenant_stats = {
            'tenant_name': customer['tenant_name'],
            'emails_processed': get_processed_count(customer['tenant_name']),
            'last_activity': get_last_activity(customer['tenant_name']),
            'errors': get_error_count(customer['tenant_name']),
            'subscription_usage': get_subscription_usage(customer['tenant_name'])
        }
        stats.append(tenant_stats)

    return jsonify(stats)
```

### Day 3-4: Fix Issues & Gather Feedback

#### Step 24: Common Issues to Watch For
- **Authentication problems** → Customers can't log in
- **Permission errors** → Can't access their emails
- **Subscription limits** → Hitting limits unexpectedly
- **Document processing** → Files not being processed correctly
- **Notification delivery** → Emails not being sent

#### Step 25: Customer Feedback Collection
```python
# Add feedback endpoint
@app.route('/api/customer/feedback', methods=['POST'])
def submit_feedback():
    tenant_name = get_customer_tenant_name(request)
    data = request.get_json()

    feedback = {
        'tenant_name': tenant_name,
        'rating': data.get('rating'),
        'comments': data.get('comments'),
        'feature_requests': data.get('feature_requests'),
        'timestamp': datetime.now().isoformat()
    }

    # Store feedback (add to database later)
    store_customer_feedback(feedback)

    return jsonify({'success': True, 'message': 'Thank you for your feedback!'})
```

### Day 5: Launch Preparation

#### Step 26: Final Launch Checklist
- ✅ **All beta customers working** → No critical issues
- ✅ **Payment system tested** → Stripe subscriptions working
- ✅ **Monitoring in place** → Can see what's happening
- ✅ **Support system ready** → Can help customers quickly
- ✅ **Marketing materials** → Website, pricing page, documentation
- ✅ **Legal documents** → Terms of service, privacy policy

## **🎯 What Customers Get When They Click the Onboarding Link**

### **Full Access Permissions (Explained Simply):**

When a customer clicks your onboarding link and grants permissions, you get:

#### ✅ **Email Access**
- **Read all emails** in mailboxes they specify
- **Send notifications** from their mailboxes (like <EMAIL> sends invoice notifications)
- **Create email folders** to organize processed emails
- **Access email attachments** to process documents

#### ✅ **OneDrive/SharePoint Access**
- **Read files** from their OneDrive Business
- **Save processed documents** to organized folders
- **Create folder structures** like `Invoices/2024/ACME-Corp/`
- **Access shared documents** if they grant permission

#### ✅ **What They Control**
- **Which mailboxes to monitor** → They choose specific email addresses
- **Which document types to process** → They enable/disable invoice processing, contract processing, etc.
- **Where to save files** → OneDrive, SharePoint, or local folders
- **Notification recipients** → Who gets notified when documents are processed
- **Revoke access anytime** → Through Microsoft admin center

### **Customer Configuration Example:**
```
📧 Monitored Mailboxes:
✅ <EMAIL> → Process invoices, save to OneDrive/Invoices/
✅ <EMAIL> → Process contracts, save to OneDrive/Contracts/
❌ <EMAIL> → Not monitored

📄 Document Types:
✅ Invoices → Extract: amount, date, vendor → Notify: <EMAIL>
✅ Contracts → Extract: parties, dates, value → Notify: <EMAIL>
❌ Purchase Orders → Not processed

💾 Storage:
✅ OneDrive Business/Mail Auto/{doc_type}/{year}/{company}/
```

## **🚀 Quick Start Commands for Each Week**

### Week 1 Commands:
```bash
# Setup Azure infrastructure
az login
az group create --name "mail-auto-prod-rg" --location "West Europe"
az keyvault create --name "mail-auto-prod-vault" --resource-group "mail-auto-prod-rg"
```

### Week 2 Commands:
```bash
# Test customer registration
python web_server.py
# Visit: http://localhost:5000/register
```

### Week 3 Commands:
```bash
# Test customer dashboard
python Test/production_scenarios.py
```

### Week 4 Commands:
```bash
# Deploy to Azure
az webapp create --resource-group "mail-auto-prod-rg" --name "mail-auto-prod"
```

### Week 5 Commands:
```bash
# Monitor beta customers
python -c "from core.tracking import get_tracking_service; print('Beta customer stats:', get_tracking_service().get_tenant_stats())"
```

## **📞 Customer Support & Maintenance Plan**

### Support Levels by Plan:
- **Starter Plan (€29/month)**:
  - Email support only
  - 48-hour response time
  - Basic documentation access

- **Business Plan (€99/month)**:
  - Priority email support
  - Live chat during business hours
  - 24-hour response time
  - Video call setup assistance

- **Enterprise Plan (€299/month)**:
  - Dedicated support manager
  - Phone support
  - 4-hour response time
  - Custom integration assistance
  - Monthly check-in calls

### Monitoring & Alerts:
```python
# Set up alerts for common issues
ALERT_CONDITIONS = {
    'subscription_limit_reached': 'Customer hits 90% of document limit',
    'authentication_failure': 'Customer can\'t access their emails',
    'processing_errors': 'More than 5 failed document processing attempts',
    'payment_failure': 'Stripe subscription payment fails',
    'high_usage': 'Customer processes 10x normal volume'
}
```

## **🎉 Success Metrics & KPIs**

### Week 1 Success:
- ✅ Azure infrastructure created without errors
- ✅ Test customer can complete onboarding flow
- ✅ Subscription limits work correctly

### Week 2 Success:
- ✅ Customer registration form works
- ✅ Real customer data shows in dashboard
- ✅ Mailbox configuration saves correctly

### Week 3 Success:
- ✅ Customer can process real emails
- ✅ Documents saved to correct locations
- ✅ Notifications sent from customer mailboxes

### Week 4 Success:
- ✅ Payment processing works
- ✅ App deployed to Azure successfully
- ✅ Custom domain with HTTPS working

### Week 5 Success:
- ✅ 5+ beta customers actively using system
- ✅ No critical bugs reported
- ✅ Positive customer feedback
- ✅ Ready for marketing launch

---

## **🚀 Ready to Start? Your First Step:**

**This Week (Week 1):** Focus on Azure infrastructure setup. Start with Step 1 (Create Resource Group) and work through each step. Don't move to Week 2 until Week 1 is completely working.

**Need Help?** Each step has simple commands you can copy and paste. If something doesn't work, the error message will tell you what's wrong.

**Testing:** After each step, test that it works before moving to the next step. This way you catch problems early when they're easy to fix.

**Remember:** You're building a professional system that will handle real customer data and payments. Take your time and do each step carefully.

**Your customers will have full control** - they choose which mailboxes to monitor, which document types to process, and can revoke access anytime. You're building a service they'll trust with their business documents.

---

## **🗑️ Tenant Management & Removal Guide**

### Understanding Customer Registration & Re-registration

When a customer accesses your onboarding link (e.g., `https://yourdomain.com/onboard/customer-name`), the system:

1. **Generates a consent URL** with a unique state parameter
2. **Redirects to Azure AD** for admin consent
3. **Receives OAuth tokens** after consent
4. **Stores credentials** in Key Vault
5. **Creates tenant configuration** in the `tenants/` directory

### Can Removed Customers Re-register?

**Yes, removed customers can use the same onboarding link again** unless you also revoke their Azure AD app consent. The onboarding process will:

- Generate new OAuth tokens
- Re-create their credentials in Key Vault
- Re-create their local configuration files

### Complete Customer Removal Process

To fully remove a customer and prevent re-registration, follow these steps:

#### Step 1: Remove Customer Data from Key Vault

**Using Azure Portal (Manual):**
1. Go to [Azure Portal](https://portal.azure.com)
2. Navigate to your Key Vault
3. Go to **Secrets**
4. Delete these secrets:
   - `tenant-{customer-name}-credentials`
   - `tenant-{customer-name}-token-cache`

**Using Python Script:**
```python
# Create remove_customer.py
from core.config import config_manager
from core.key_vault_service import key_vault_service
import sys

def remove_customer_from_keyvault(customer_name: str):
    """Remove customer credentials and token cache from Key Vault"""
    print(f"Removing customer '{customer_name}' from Key Vault...")

    success = key_vault_service.delete_tenant_data(customer_name)
    if success:
        print(f"✅ Successfully removed {customer_name} from Key Vault")
    else:
        print(f"❌ Failed to remove {customer_name} from Key Vault")

    return success

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python remove_customer.py <customer_name>")
        sys.exit(1)

    customer_name = sys.argv[1]
    remove_customer_from_keyvault(customer_name)
```

**Run the script:**
```bash
python remove_customer.py customer-name
```

#### Step 2: Remove Local Customer Files

```bash
# Remove entire customer directory
rm -rf tenants/customer-name

# Or on Windows
rmdir /s tenants\customer-name
```

**What gets removed:**
- `tenants/customer-name/config.json` - Customer configuration
- `tenants/customer-name/credentials.json` - Local credentials (if any)
- `tenants/customer-name/token_cache.json` - Local token cache (if any)
- `tenants/customer-name/backup/` - Any backup files

#### Step 3: Revoke Azure AD App Consent (Prevent Re-registration)

**Manual Method (Recommended):**

1. Go to [Azure Portal](https://portal.azure.com)
2. Navigate to **Azure Active Directory**
3. Go to **Enterprise applications**
4. Search for your app registration name
5. Click on your application
6. Go to **Permissions**
7. Find the customer you want to remove
8. Click **Remove consent** or **Revoke consent**

#### Step 4: Verify Complete Removal

**Check Key Vault:**
1. Go to Azure Portal → Your Key Vault → Secrets
2. Verify no secrets exist for the customer

**Check Local Files:**
```bash
# Verify customer directory is removed
ls tenants/ | grep customer-name
```

**Test Re-registration Prevention:**
1. Try accessing the onboarding URL: `https://yourdomain.com/onboard/customer-name`
2. The customer should be prompted for admin consent again
3. If consent was properly revoked, they'll need to re-consent

### Complete Customer Removal Script

Create a comprehensive removal script:

```python
# complete_customer_removal.py
import os
import sys
import shutil
import time
from core.config import config_manager
from core.key_vault_service import key_vault_service

def remove_customer_completely(customer_name: str, tenants_dir: str = "tenants"):
    """
    Completely remove a customer from the system.

    Args:
        customer_name: Name of the customer to remove
        tenants_dir: Directory containing customer folders
    """
    print(f"🗑️  Starting complete removal of customer: {customer_name}")

    # Step 1: Remove from Key Vault
    print("\n1. Removing from Key Vault...")
    if config_manager.config.use_key_vault:
        kv_success = key_vault_service.delete_tenant_data(customer_name)
        if kv_success:
            print(f"   ✅ Removed {customer_name} from Key Vault")
        else:
            print(f"   ⚠️  Failed to remove {customer_name} from Key Vault")
    else:
        print("   ℹ️  Key Vault not enabled, skipping")

    # Step 2: Remove local files
    print("\n2. Removing local files...")
    customer_path = os.path.join(tenants_dir, customer_name)

    if os.path.exists(customer_path):
        try:
            # Create backup before removal
            backup_path = f"{customer_path}_removed_backup_{int(time.time())}"
            shutil.move(customer_path, backup_path)
            print(f"   ✅ Moved customer files to backup: {backup_path}")
        except Exception as e:
            print(f"   ❌ Failed to remove local files: {e}")
    else:
        print(f"   ℹ️  No local files found for {customer_name}")

    # Step 3: Instructions for Azure AD consent removal
    print(f"\n3. Manual step required - Revoke Azure AD consent:")
    print(f"   📋 Go to Azure Portal → Azure Active Directory → Enterprise applications")
    print(f"   📋 Search for your app registration")
    print(f"   📋 Go to Permissions and revoke consent for customer: {customer_name}")
    print(f"   📋 This prevents the customer from re-registering automatically")

    print(f"\n✅ Customer removal completed for: {customer_name}")
    print(f"⚠️  Remember to manually revoke Azure AD consent to prevent re-registration!")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python complete_customer_removal.py <customer_name>")
        print("Example: python complete_customer_removal.py customer-name")
        sys.exit(1)

    customer_name = sys.argv[1]

    # Confirmation prompt
    response = input(f"Are you sure you want to completely remove customer '{customer_name}'? (yes/no): ")
    if response.lower() != 'yes':
        print("Removal cancelled.")
        sys.exit(0)

    remove_customer_completely(customer_name)
```

### Security Considerations for Customer Removal

#### Soft Delete Protection
- **Key Vault soft delete**: Deleted secrets are retained for 90 days
- **Recovery possible**: Use Azure Portal to recover if needed
- **Permanent deletion**: Use "Purge" option (if purge protection is disabled)

#### Audit Trail
- **Key Vault logs**: All deletion operations are logged
- **Application logs**: Removal activities should be logged
- **Backup verification**: Ensure backups exist before removal

#### Data Retention Compliance
- **Check legal requirements**: Some jurisdictions require data retention
- **Customer contracts**: Review SLAs for data deletion timelines
- **Backup policies**: Ensure compliance with backup retention policies

### Preventing Accidental Re-registration

#### Option 1: Implement Registration Blacklist
```python
# Add to tenant_onboarding.py
BLACKLISTED_CUSTOMERS = [
    "removed-customer-1",
    "removed-customer-2"
]

@app.route("/onboard/<customer_name>")
def onboard_customer(customer_name: str):
    if customer_name in BLACKLISTED_CUSTOMERS:
        return jsonify({
            "error": "Access denied",
            "message": "This customer has been removed from the system"
        }), 403

    # Continue with normal onboarding...
```

#### Option 2: Require Admin Approval
```python
# Implement approval workflow
@app.route("/onboard/<customer_name>")
def onboard_customer(customer_name: str):
    # Check if customer needs approval
    if requires_approval(customer_name):
        return redirect(f"/approval-required/{customer_name}")

    # Continue with normal onboarding...
```
