"""
Subscription Management System for Mail Auto
Handles subscription plans, usage limits, and billing integration.
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class SubscriptionPlan(Enum):
    STARTER = "starter"
    BUSINESS = "business"
    ENTERPRISE = "enterprise"


class SubscriptionStatus(Enum):
    ACTIVE = "active"
    PAST_DUE = "past_due"
    CANCELED = "canceled"
    SUSPENDED = "suspended"


@dataclass
class PlanLimits:
    """Defines limits for each subscription plan"""
    max_mailboxes: int
    max_documents_per_month: int
    max_storage_gb: int
    support_level: str
    features: List[str]


@dataclass
class UsageStats:
    """Current usage statistics for a tenant"""
    current_mailboxes: int
    documents_this_month: int
    storage_used_gb: float
    last_updated: datetime


class SubscriptionManager:
    """
    Manages subscription plans, usage tracking, and limit enforcement.
    """
    
    # Define subscription plan limits
    PLAN_LIMITS = {
        SubscriptionPlan.STARTER: PlanLimits(
            max_mailboxes=2,
            max_documents_per_month=100,
            max_storage_gb=1,
            support_level="email",
            features=["basic_processing", "email_notifications"]
        ),
        SubscriptionPlan.BUSINESS: PlanLimits(
            max_mailboxes=10,
            max_documents_per_month=1000,
            max_storage_gb=10,
            support_level="priority",
            features=["basic_processing", "email_notifications", "advanced_analytics", "custom_templates"]
        ),
        SubscriptionPlan.ENTERPRISE: PlanLimits(
            max_mailboxes=50,
            max_documents_per_month=10000,
            max_storage_gb=100,
            support_level="dedicated",
            features=["basic_processing", "email_notifications", "advanced_analytics", "custom_templates", "api_access", "sla_guarantee"]
        )
    }
    
    def __init__(self, tenant_name: str):
        """
        Initialize subscription manager for a specific tenant.
        
        Args:
            tenant_name: Name of the tenant
        """
        self.tenant_name = tenant_name
        self._subscription_data = self._load_subscription_data()
    
    def _load_subscription_data(self) -> Dict[str, Any]:
        """Load subscription data for the tenant"""
        try:
            subscription_file = f"tenants/{self.tenant_name}/subscription.json"
            with open(subscription_file, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            # Create default subscription for new tenants
            return self._create_default_subscription()
        except Exception as e:
            logger.error(f"Failed to load subscription data for {self.tenant_name}: {e}")
            return self._create_default_subscription()
    
    def _create_default_subscription(self) -> Dict[str, Any]:
        """Create default subscription (starter plan)"""
        return {
            "plan": SubscriptionPlan.STARTER.value,
            "status": SubscriptionStatus.ACTIVE.value,
            "created_date": datetime.now().isoformat(),
            "next_billing_date": (datetime.now() + timedelta(days=30)).isoformat(),
            "usage": {
                "current_mailboxes": 0,
                "documents_this_month": 0,
                "storage_used_gb": 0.0,
                "last_reset": datetime.now().replace(day=1).isoformat()
            },
            "billing": {
                "customer_id": None,
                "payment_method": None,
                "last_payment": None
            }
        }
    
    def _save_subscription_data(self):
        """Save subscription data to file"""
        try:
            import os
            os.makedirs(f"tenants/{self.tenant_name}", exist_ok=True)
            subscription_file = f"tenants/{self.tenant_name}/subscription.json"
            with open(subscription_file, 'w') as f:
                json.dump(self._subscription_data, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save subscription data for {self.tenant_name}: {e}")
    
    def get_plan_limits(self) -> PlanLimits:
        """Get limits for current subscription plan"""
        plan = SubscriptionPlan(self._subscription_data["plan"])
        return self.PLAN_LIMITS[plan]
    
    def get_usage_stats(self) -> UsageStats:
        """Get current usage statistics"""
        usage = self._subscription_data["usage"]
        return UsageStats(
            current_mailboxes=usage["current_mailboxes"],
            documents_this_month=usage["documents_this_month"],
            storage_used_gb=usage["storage_used_gb"],
            last_updated=datetime.fromisoformat(usage["last_reset"])
        )
    
    def can_add_mailbox(self) -> bool:
        """Check if tenant can add another mailbox"""
        limits = self.get_plan_limits()
        usage = self.get_usage_stats()
        return usage.current_mailboxes < limits.max_mailboxes
    
    def can_process_document(self) -> bool:
        """Check if tenant can process another document this month"""
        limits = self.get_plan_limits()
        usage = self.get_usage_stats()
        return usage.documents_this_month < limits.max_documents_per_month
    
    def increment_mailbox_count(self):
        """Increment mailbox count (call when adding mailbox)"""
        if self.can_add_mailbox():
            self._subscription_data["usage"]["current_mailboxes"] += 1
            self._save_subscription_data()
            return True
        return False
    
    def decrement_mailbox_count(self):
        """Decrement mailbox count (call when removing mailbox)"""
        if self._subscription_data["usage"]["current_mailboxes"] > 0:
            self._subscription_data["usage"]["current_mailboxes"] -= 1
            self._save_subscription_data()
    
    def increment_document_count(self):
        """Increment document count (call when processing document)"""
        if self.can_process_document():
            self._subscription_data["usage"]["documents_this_month"] += 1
            self._save_subscription_data()
            return True
        return False
    
    def reset_monthly_usage(self):
        """Reset monthly usage counters (call on billing cycle)"""
        self._subscription_data["usage"]["documents_this_month"] = 0
        self._subscription_data["usage"]["last_reset"] = datetime.now().isoformat()
        self._save_subscription_data()
    
    def upgrade_plan(self, new_plan: SubscriptionPlan) -> bool:
        """Upgrade subscription plan while preserving usage data"""
        try:
            # Store current usage before upgrade
            current_usage = self._subscription_data["usage"].copy()

            self._subscription_data["plan"] = new_plan.value
            self._subscription_data["upgraded_date"] = datetime.now().isoformat()

            # Preserve current usage data
            self._subscription_data["usage"] = current_usage

            self._save_subscription_data()
            logger.info(f"Upgraded {self.tenant_name} to {new_plan.value}")
            return True
        except Exception as e:
            logger.error(f"Failed to upgrade plan for {self.tenant_name}: {e}")
            return False
    
    def get_subscription_info(self) -> Dict[str, Any]:
        """Get complete subscription information"""
        limits = self.get_plan_limits()
        usage = self.get_usage_stats()
        
        return {
            "tenant_name": self.tenant_name,
            "plan": self._subscription_data["plan"],
            "status": self._subscription_data["status"],
            "limits": {
                "max_mailboxes": limits.max_mailboxes,
                "max_documents_per_month": limits.max_documents_per_month,
                "max_storage_gb": limits.max_storage_gb,
                "support_level": limits.support_level,
                "features": limits.features
            },
            "usage": {
                "current_mailboxes": usage.current_mailboxes,
                "documents_this_month": usage.documents_this_month,
                "storage_used_gb": usage.storage_used_gb,
                "mailboxes_remaining": limits.max_mailboxes - usage.current_mailboxes,
                "documents_remaining": limits.max_documents_per_month - usage.documents_this_month
            },
            "next_billing_date": self._subscription_data["next_billing_date"]
        }


def get_subscription_manager(tenant_name: str) -> SubscriptionManager:
    """Factory function to get subscription manager for a tenant"""
    return SubscriptionManager(tenant_name)
