#!/usr/bin/env python3
"""
Test script for enhanced email body interpretation.

This script specifically tests how well the AI interprets instructions,
links, and context provided in email body text, and incorporates them
into the document summary.
"""

import sys
import os

# Add the parent directory to the path so we can import core modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.interpreter.chatgpt_api import analyze_mail_and_pdf

def test_email_interpretation(test_name, email_body, pdf_text):
    """Helper function to test email body interpretation."""
    print(f"\n{'='*60}")
    print(f"TEST: {test_name}")
    print('='*60)
    print(f"📧 Email Body:")
    print(email_body)
    print(f"\n📄 Document Content (abbreviated):")
    print(pdf_text[:200] + "..." if len(pdf_text) > 200 else pdf_text)
    
    result = analyze_mail_and_pdf(email_body, pdf_text)
    
    print(f"\n🤖 AI Analysis:")
    print(f"Document Type: {result.get('doc_type', 'Unknown')}")
    print(f"\nSummary:")
    print(result.get('summary', 'No summary'))
    
    return result

def test_email_with_links():
    """Test email containing specific links and instructions."""
    email_body = """
    Hi Quality Team,
    
    Please review the attached CoA and compare it with our specifications at:
    https://specs.company.com/materials/batch-ABC123
    
    If everything looks good, please update the approval status in our system:
    https://quality.company.com/approvals
    
    The customer is waiting for our response by 3 PM today.
    
    Thanks,
    QA Manager
    """
    
    pdf_text = """
    CERTIFICATE OF ANALYSIS
    Batch: ABC123
    Product: Chemical XYZ
    
    pH: 7.1 (Spec: 7.0-7.5)
    Purity: 99.6% (Spec: ≥99.5%)
    Moisture: 0.3% (Spec: ≤0.5%)
    
    All tests passed.
    """
    
    return test_email_interpretation("Email with Links and Deadlines", email_body, pdf_text)

def test_email_with_urgency():
    """Test email with urgency indicators."""
    email_body = """
    URGENT - IMMEDIATE ATTENTION REQUIRED
    
    The attached safety report shows concerning levels in Area 3.
    Please review immediately and call me at ext. 911.
    
    We may need to shut down operations until this is resolved.
    
    Emergency contact: <EMAIL>
    
    Safety Manager
    """
    
    pdf_text = """
    SAFETY MONITORING REPORT
    Area: Production Area 3
    Date: Today
    
    Gas Levels:
    CO: 45 ppm (Limit: 35 ppm) ⚠️ EXCEEDED
    H2S: 8 ppm (Limit: 10 ppm) OK
    O2: 19.5% (Range: 19.5-23.5%) OK
    
    RECOMMENDATION: Immediate ventilation check required.
    """
    
    return test_email_interpretation("Urgent Safety Alert", email_body, pdf_text)

def test_email_with_context():
    """Test email providing important context."""
    email_body = """
    Hi Team,
    
    This is the CoA for the replacement batch we discussed in yesterday's meeting.
    Remember, the previous batch (ABC122) failed due to high moisture content.
    
    Please pay special attention to the moisture levels and compare with our
    discussion notes from the meeting.
    
    If this batch also fails, we'll need to contact the supplier about their
    drying process.
    
    Production Manager
    """
    
    pdf_text = """
    CERTIFICATE OF ANALYSIS
    Batch: ABC123 (Replacement for ABC122)
    Product: Hygroscopic Chemical
    
    Moisture Content: 0.8% (Spec: ≤1.0%)
    pH: 7.2 (Spec: 7.0-7.5)
    Purity: 99.7% (Spec: ≥99.5%)
    
    Previous batch ABC122 rejected for moisture: 1.3%
    """
    
    return test_email_interpretation("Email with Historical Context", email_body, pdf_text)

def test_email_with_specific_requests():
    """Test email with specific review requests."""
    email_body = """
    Dear Lab Team,
    
    Please review the attached calibration certificate and specifically check:
    
    1. Are all measurement points within tolerance?
    2. Is the next calibration date acceptable for our audit schedule?
    3. Do we need to order any replacement parts mentioned?
    
    Please respond with your recommendations by Friday.
    
    Also, please update our equipment database with the new calibration date.
    
    Maintenance Supervisor
    """
    
    pdf_text = """
    CALIBRATION CERTIFICATE
    Equipment: pH Meter PM-001
    
    Calibration Points:
    pH 4.00: Measured 4.01 (Tolerance ±0.05) ✓
    pH 7.00: Measured 6.99 (Tolerance ±0.05) ✓
    pH 10.00: Measured 10.02 (Tolerance ±0.05) ✓
    
    Next Calibration Due: December 2024
    
    Notes: Electrode showing signs of wear, recommend replacement within 6 months.
    """
    
    return test_email_interpretation("Email with Specific Review Checklist", email_body, pdf_text)

def test_email_with_approval_workflow():
    """Test email describing approval workflow."""
    email_body = """
    Hi Finance Team,
    
    Please process the attached invoice following our new approval workflow:
    
    Step 1: Verify amounts against PO-2024-089
    Step 2: Check budget availability in cost center 1234
    Step 3: Get approval from department head if >$5000
    Step 4: Process payment within 15 days for 2% discount
    
    The vendor offers early payment discount, so please prioritize this.
    
    Accounts Payable
    """
    
    pdf_text = """
    INVOICE #INV-2024-445
    Amount: $6,750.00
    PO Reference: PO-2024-089
    
    Payment Terms: 2/15 Net 30
    (2% discount if paid within 15 days)
    
    Items:
    - Laboratory Equipment: $6,000.00
    - Installation Service: $750.00
    
    Total: $6,750.00
    """
    
    return test_email_interpretation("Email with Workflow Instructions", email_body, pdf_text)

def test_simple_email():
    """Test simple email without special instructions."""
    email_body = """
    Hi,
    
    Please find attached document.
    
    Thanks,
    John
    """
    
    pdf_text = """
    CERTIFICATE OF ANALYSIS
    Batch: XYZ789
    
    All parameters within specification.
    Approved for release.
    """
    
    return test_email_interpretation("Simple Email (Baseline)", email_body, pdf_text)

def main():
    """Run email body interpretation tests."""
    print("Email Body Interpretation Test Suite")
    print("===================================")
    print("Testing how AI interprets email instructions and context...")
    
    try:
        # Test various types of email instructions
        test_email_with_links()
        test_email_with_urgency()
        test_email_with_context()
        test_email_with_specific_requests()
        test_email_with_approval_workflow()
        test_simple_email()
        
        print("\n" + "="*60)
        print("EMAIL INTERPRETATION TESTS COMPLETE")
        print("="*60)
        print("Review the summaries above to verify:")
        print("✓ Links and URLs are mentioned in summaries")
        print("✓ Urgency indicators are reflected in analysis")
        print("✓ Historical context is incorporated")
        print("✓ Specific requests are highlighted")
        print("✓ Workflow instructions are included")
        print("✓ Simple emails still get good analysis")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        print("Make sure you have OPENAI_API_KEY set in your environment")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
