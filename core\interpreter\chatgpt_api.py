"""OpenAI ChatGPT-4o integration helpers.

This module provides a single public function – ``analyze_mail_and_pdf`` – that
sends the *email body* and extracted *PDF text* to OpenAI's chat completions
endpoint and returns the model's structured JSON response as a ``dict``.

Environment variables
---------------------
OPENAI_API_KEY
    The secret API key used for authenticating with the OpenAI service.

Any networking or JSON related error is retried with exponential back-off up to
``MAX_RETRIES`` times. If all attempts fail the function returns a minimal
fallback dict so that the caller can continue processing without crashing.
"""

from __future__ import annotations

from dotenv import load_dotenv
load_dotenv()

from typing import Dict, Any, List
import os
import json
import time
import logging
import requests

__all__ = ["analyze_mail_and_pdf"]

log = logging.getLogger(__name__)

OPENAI_API_URL = "https://api.openai.com/v1/chat/completions"
MODEL = "gpt-4o"
TIMEOUT = 30  # seconds per request
MAX_RETRIES = 3
BACKOFF_SECS = 2


_SYSTEM_PROMPT = (
    "You are an intelligent document processing assistant integrated into an "
    "email automation pipeline. You receive the TEXT BODY of an e-mail and "
    "the extracted TEXT from a PDF attachment. Analyse BOTH together to: "
    "(1) identify the high-level document type (e.g. invoice, certificate of "
    "analysis, offer, safety data sheet, purchase order, etc.), "
    "(2) extract any key/value data that would be useful for downstream systems, "
    "and (3) output a structured, detailed summary that provides clear insights. "
    "\n\nEMAIL BODY INTERPRETATION:\n"
    "- Pay special attention to instructions, requests, or specific guidance provided in the email body\n"
    "- If the sender mentions specific links, actions, or requests in the email, include these in the summary\n"
    "- ALWAYS capture contact information: phone numbers, extensions, email addresses, emergency contacts\n"
    "- Include any deadlines, time constraints, or urgency indicators\n"
    "- Incorporate any context or background information from the email that helps understand the document's purpose\n"
    "- Note any special handling instructions, approval workflows, or required actions mentioned in the email\n\n"
    "IMPORTANT EXTRACTION RULES:\n"
    "- When extracting batch numbers, lot numbers, or serial numbers: ONLY extract "
    "the actual alphanumeric identifier that follows labels like 'Batch:', 'Lot:', "
    "'Serial:', etc. NEVER extract dates as batch/lot/serial numbers.\n"
    "- Dates should be extracted separately as date fields (e.g. 'manufacturing_date', "
    "'expiry_date', 'test_date', 'signing_date', 'document_date', 'batch_date', etc.).\n"
    "- If batch numbers contain dates (e.g. '2018-11-08'), extract the date as 'batch_date'.\n"
    "- Always extract company/supplier/manufacturer names as 'company_name'.\n"
    "- Be precise with field names - use descriptive names like 'batch_number', "
    "'lot_number', 'invoice_number', 'po_number', etc.\n"
    "- If a field contains both letters and numbers, include the complete identifier.\n\n"
    "INTELLIGENT DOCUMENT ANALYSIS:\n"
    "- Automatically determine if the document type requires parameter/specification analysis\n"
    "- Documents that typically need parameter analysis include:\n"
    "  * Certificate of Analysis (CoA) - test results vs specifications\n"
    "  * Quality Control Reports - measurements vs acceptable ranges\n"
    "  * Test Reports - performance metrics vs standards\n"
    "  * Inspection Reports - findings vs compliance requirements\n"
    "  * Safety Data Sheets - hazard levels vs safety thresholds\n"
    "  * Environmental Reports - emissions/levels vs regulatory limits\n"
    "  * Calibration Certificates - accuracy vs tolerance requirements\n"
    "  * Material Certificates - properties vs material standards\n"
    "- For documents requiring parameter analysis:\n"
    "  * SMART PARAMETER REPORTING: Only report parameters that are:\n"
    "    - Out of specification (failed)\n"
    "    - Close to specification limits (within 10% of limit)\n"
    "    - Specifically mentioned in email instructions\n"
    "    - Critical safety parameters\n"
    "  * Do NOT list all parameters if they are well within specification\n"
    "  * Summarize compliant parameters as 'All other parameters within specification'\n"
    "  * Identify specification limits, target values, acceptable ranges, or compliance criteria\n"
    "  * Compare actual results/values against these specifications\n"
    "  * Flag any parameters that are out of specification, borderline, or concerning\n"
    "  * Assess overall compliance status\n"
    "- Provide intelligent actionable guidance based on analysis:\n"
    "  * If all parameters meet specifications: 'Please review the attachment if you wish (optional review)'\n"
    "  * If minor deviations or borderline results: 'Please review the attachment - attention recommended for [specific areas]'\n"
    "  * If significant issues or non-compliance: 'Please review the attachment - attention required for [specific parameters]'\n"
    "  * If critical failures or safety concerns: 'URGENT: Please review the attachment immediately - critical issues identified'\n"
    "- For other document types, focus on the most critical information relevant to that document type\n\n"
    "SUMMARY FORMATTING RULES:\n"
    "- Create structured, detailed summaries using CLEAR SECTIONS with separators\n"
    "- Use this exact section structure for better readability:\n"
    "\n"
    "--- DOCUMENT INFO ---\n"
    "[Document type, key identifiers, dates, etc.]\n"
    "\n"
    "--- KEY FINDINGS ---\n"
    "[Critical results, compliance status, important parameters only]\n"
    "\n"
    "--- EMAIL INSTRUCTIONS ---\n"
    "[Instructions from email, contact info, deadlines, specific links mentioned]\n"
    "\n"
    "--- ACTION REQUIRED ---\n"
    "[Clear action items and recommendations]\n"
    "\n"
    "- IMPORTANT FORMATTING RULES:\n"
    "  * Use bullet points (•) within each section for multiple items\n"
    "  * Include actual URLs/links mentioned in emails, not generic 'provided link' text\n"
    "  * For technical documents: Only report concerning parameters, summarize others as 'All other parameters within specification'\n"
    "  * ALWAYS include contact information from emails: phone numbers, extensions, emergency contacts, email addresses\n"
    "  * Avoid redundant phrases - do not repeat 'Please review the attachment' multiple times\n"
    "  * Prioritize the most critical information that requires recipient attention\n\n"
    "Return your answer STRICTLY as a JSON object with these top-level keys: doc_type, "
    "summary, extracted_fields. The 'summary' must be a STRING containing the formatted text with sections. "
    "``extracted_fields`` must itself be a JSON object, or an empty object if not applicable. "
    "DO NOT wrap the JSON in markdown or any prose – output ONLY valid minified JSON."
)


def _build_messages(mail_body: str, pdf_text: str, language: str = "English") -> List[Dict[str, str]]:
    """Compose the messages list for the Chat Completions endpoint."""

    user_prompt = (
        "EMAIL BODY:\n" + mail_body.strip() + "\n\n" +
        "PDF TEXT:\n" + pdf_text.strip() + "\n"
    )

    # Add language instruction to system prompt if not English
    system_prompt = _SYSTEM_PROMPT
    if language.lower() != "english":
        system_prompt += f"\n\nIMPORTANT: Generate the summary in {language} language, but keep field names in English for system compatibility."

    return [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_prompt},
    ]


def _post_chat(messages: List[Dict[str, str]]) -> Dict[str, Any]:
    """Low-level HTTP POST helper with retries and exponential back-off."""
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        raise RuntimeError("Environment variable OPENAI_API_KEY is not set.")

    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
    }

    payload = {
        "model": MODEL,
        "messages": messages,
        "temperature": 0.0,
        "response_format": {"type": "json_object"},
    }

    for attempt in range(1, MAX_RETRIES + 1):
        try:
            resp = requests.post(
                OPENAI_API_URL,
                headers=headers,
                json=payload,
                timeout=TIMEOUT,
            )
            resp.raise_for_status()
            data = resp.json()
            return data  # type: ignore[return-value]
        except (requests.RequestException, ValueError) as exc:
            log.warning("OpenAI request failed (attempt %s/%s): %s", attempt, MAX_RETRIES, exc)
            if attempt == MAX_RETRIES:
                raise
            time.sleep(BACKOFF_SECS ** attempt)
    # Unreachable – loop either returns or raises.


def analyze_mail_and_pdf(mail_body: str, pdf_text: str, language: str = "English") -> Dict[str, Any]:
    """Send *mail_body* and *pdf_text* to ChatGPT-4o and return its JSON dict.

    If the request fails the function logs the error and returns a fallback
    result so that the caller can keep running without the ML component.

    Args:
        mail_body: The email body text
        pdf_text: The extracted PDF text
        language: The preferred language for the summary (default: English)
    """
    messages = _build_messages(mail_body, pdf_text, language)

    try:
        raw = _post_chat(messages)
        content = raw["choices"][0]["message"]["content"].strip()
        # Debug: log first 500 chars of the model's reply to diagnose JSON issues
        log.debug("RAW ChatGPT reply: %s", content[:500])
        # The model has been instructed to output JSON only.  Parse it.
        result: Dict[str, Any] = json.loads(content)
        return result
    except Exception as exc:  # broad catch so the pipeline never aborts here
        log.error("ChatGPT analysis failed – falling back to defaults: %s", exc)
        return {
            "doc_type": "Unknown",
            "summary": "ChatGPT analysis failed.",
            "extracted_fields": {},
        }