#!/usr/bin/env python3
"""
Test script to verify the fix for safety alert contact information capture.

This test specifically verifies that emergency contacts and phone numbers
are properly captured and included in the summary.
"""

import sys
import os

# Add the parent directory to the path so we can import core modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.interpreter.chatgpt_api import analyze_mail_and_pdf

def test_safety_alert_contacts():
    """Test that emergency contacts are properly captured in safety alerts."""
    print("🚨 Testing Safety Alert Contact Information Capture")
    print("=" * 60)
    
    email_body = """
    URGENT - IMMEDIATE ATTENTION REQUIRED
    
    The attached safety report shows concerning levels in Area 3.
    Please review immediately and call me at ext. 911.
    
    We may need to shut down operations until this is resolved.
    
    Emergency contact: <EMAIL>
    
    Safety Manager
    """
    
    pdf_text = """
    SAFETY MONITORING REPORT
    Area: Production Area 3
    Date: Today
    
    Gas Levels:
    CO: 45 ppm (Limit: 35 ppm) ⚠️ EXCEEDED
    H2S: 8 ppm (Limit: 10 ppm) OK
    O2: 19.5% (Range: 19.5-23.5%) OK
    
    RECOMMENDATION: Immediate ventilation check required.
    """
    
    result = analyze_mail_and_pdf(email_body, pdf_text)
    
    print(f"📄 Document Type: {result.get('doc_type', 'Unknown')}")
    print(f"\n📋 Summary:")
    print(result.get('summary', 'No summary'))
    
    # Check if contacts are captured
    summary = result.get('summary', '')
    
    print(f"\n🔍 Verification:")
    print("=" * 40)
    
    checks = [
        ("Extension 911 mentioned", "ext. 911" in summary.lower() or "extension 911" in summary.lower()),
        ("Emergency email mentioned", "<EMAIL>" in summary.lower()),
        ("CO level issue highlighted", "co" in summary.lower() and "45 ppm" in summary),
        ("Urgency indicated", "urgent" in summary.lower() or "immediate" in summary.lower()),
        ("Bullet point formatting", "•" in summary or "-" in summary),
        ("Action items provided", "action" in summary.lower() or "review" in summary.lower())
    ]
    
    all_passed = True
    for check_name, passed in checks:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} {check_name}")
        if not passed:
            all_passed = False
    
    print(f"\n{'🎉 ALL CHECKS PASSED!' if all_passed else '⚠️ Some checks failed'}")
    
    return all_passed

def test_multiple_contacts():
    """Test with multiple contact methods."""
    print("\n" + "=" * 60)
    print("🔗 Testing Multiple Contact Methods")
    print("=" * 60)
    
    email_body = """
    Hi Team,
    
    Please review the attached calibration report.
    
    If you have questions:
    - Call me at (*************
    - Or email: <EMAIL>
    - Emergency after hours: (*************
    - Text updates to: +1-************
    
    Please respond by Friday 5 PM.
    
    John Smith
    Calibration Manager
    """
    
    pdf_text = """
    CALIBRATION REPORT
    Equipment: Pressure Gauge PG-001
    
    All calibration points within tolerance.
    Next calibration due: March 2025
    """
    
    result = analyze_mail_and_pdf(email_body, pdf_text)
    
    print(f"📄 Document Type: {result.get('doc_type', 'Unknown')}")
    print(f"\n📋 Summary:")
    print(result.get('summary', 'No summary'))
    
    # Check if multiple contacts are captured
    summary = result.get('summary', '')
    
    print(f"\n🔍 Contact Capture Verification:")
    print("=" * 40)
    
    contact_checks = [
        ("Main phone number", "(*************" in summary),
        ("Email address", "<EMAIL>" in summary),
        ("Emergency number", "(*************" in summary),
        ("Text number", "+1-************" in summary or "************" in summary),
        ("Deadline mentioned", "friday" in summary.lower() and "5 pm" in summary.lower())
    ]
    
    contacts_captured = 0
    for check_name, passed in contact_checks:
        status = "✅ CAPTURED" if passed else "❌ MISSED"
        print(f"{status} {check_name}")
        if passed:
            contacts_captured += 1
    
    print(f"\n📊 Contact Capture Rate: {contacts_captured}/{len(contact_checks)} contacts captured")
    
    return contacts_captured >= 3  # At least 3 out of 5 contacts should be captured

def main():
    """Run the safety alert contact capture tests."""
    print("Safety Alert Contact Information Test")
    print("====================================")
    
    try:
        test1_passed = test_safety_alert_contacts()
        test2_passed = test_multiple_contacts()
        
        print("\n" + "=" * 60)
        print("📊 FINAL RESULTS")
        print("=" * 60)
        
        if test1_passed and test2_passed:
            print("🎉 ALL TESTS PASSED!")
            print("✅ Emergency contacts are properly captured")
            print("✅ Summaries are well-formatted with bullet points")
            print("✅ Contact information is consistently included")
            return True
        else:
            print("⚠️ Some tests failed")
            print(f"Safety Alert Test: {'PASS' if test1_passed else 'FAIL'}")
            print(f"Multiple Contacts Test: {'PASS' if test2_passed else 'FAIL'}")
            return False
            
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        print("Make sure you have OPENAI_API_KEY set in your environment")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
