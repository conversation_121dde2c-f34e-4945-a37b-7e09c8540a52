#!/usr/bin/env python3
"""
Test script for enhanced ChatGPT analysis functionality.

This script tests the improved ChatGPT analysis that:
1. Better interprets email body instructions
2. Performs intelligent parameter analysis for various document types
3. Provides smart actionable guidance based on document analysis

Run this script to verify the enhanced analysis works correctly.
"""

import sys
import os
import json
from typing import Dict, Any

# Add the parent directory to the path so we can import core modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.interpreter.chatgpt_api import analyze_mail_and_pdf

def test_coa_with_good_parameters():
    """Test CoA document with all parameters within specification."""
    print("\n" + "="*60)
    print("TEST 1: Certificate of Analysis - All Parameters Good")
    print("="*60)
    
    email_body = """
    Hi team,
    
    Please find attached the Certificate of Analysis for batch ABC123.
    This is for the new material we discussed in yesterday's meeting.
    
    Let me know if you need any additional information or have questions.
    
    Best regards,
    John
    """
    
    pdf_text = """
    CERTIFICATE OF ANALYSIS
    
    Product: High Purity Chemical XYZ
    Batch Number: ABC123
    Manufacturing Date: 2024-01-15
    Expiry Date: 2026-01-15
    
    TEST RESULTS:
    Parameter          Result    Specification    Status
    pH                 7.2       7.0 - 7.5        PASS
    Moisture Content   0.8%      ≤ 1.0%          PASS
    Purity            99.7%      ≥ 99.5%         PASS
    Heavy Metals      <0.1 ppm   ≤ 0.5 ppm       PASS
    
    Overall Status: APPROVED
    Tested by: Quality Control Lab
    Date: 2024-01-16
    """
    
    result = analyze_mail_and_pdf(email_body, pdf_text)
    
    print(f"Document Type: {result.get('doc_type', 'Unknown')}")
    print(f"Summary:\n{result.get('summary', 'No summary')}")
    print(f"Extracted Fields: {json.dumps(result.get('extracted_fields', {}), indent=2)}")
    
    return result

def test_coa_with_concerning_parameters():
    """Test CoA document with some parameters out of specification."""
    print("\n" + "="*60)
    print("TEST 2: Certificate of Analysis - Parameters Out of Spec")
    print("="*60)
    
    email_body = """
    Hi Quality Team,
    
    Urgent: Please review the attached CoA for batch DEF456.
    The customer is waiting for our approval to proceed with production.
    
    Please check all parameters carefully and let me know ASAP.
    
    Thanks,
    Sarah
    """
    
    pdf_text = """
    CERTIFICATE OF ANALYSIS
    
    Product: Industrial Chemical ABC
    Batch Number: DEF456
    Manufacturing Date: 2024-01-10
    
    TEST RESULTS:
    Parameter          Result    Specification    Status
    pH                 6.8       7.0 - 7.5        FAIL
    Moisture Content   1.2%      ≤ 1.0%          FAIL
    Purity            99.3%      ≥ 99.5%         FAIL
    Heavy Metals      0.3 ppm    ≤ 0.5 ppm       PASS
    Viscosity         45 cP      40-50 cP        PASS
    
    Overall Status: REJECTED
    Tested by: QC Lab
    Date: 2024-01-11
    """
    
    result = analyze_mail_and_pdf(email_body, pdf_text)
    
    print(f"Document Type: {result.get('doc_type', 'Unknown')}")
    print(f"Summary:\n{result.get('summary', 'No summary')}")
    print(f"Extracted Fields: {json.dumps(result.get('extracted_fields', {}), indent=2)}")
    
    return result

def test_safety_data_sheet():
    """Test Safety Data Sheet analysis."""
    print("\n" + "="*60)
    print("TEST 3: Safety Data Sheet Analysis")
    print("="*60)
    
    email_body = """
    Dear Safety Team,
    
    Please review the attached Safety Data Sheet for the new solvent we plan to use.
    Pay special attention to the handling requirements and storage conditions.
    
    We need your approval before we can start using this in production.
    
    Best,
    Mike
    """
    
    pdf_text = """
    SAFETY DATA SHEET
    
    Product: Methylene Chloride
    Version: 2.1
    Date: 2024-01-15
    
    HAZARD IDENTIFICATION:
    - Carcinogen Category 2
    - Acute Toxicity Category 4
    - Skin Irritation Category 2
    
    EXPOSURE LIMITS:
    TWA: 25 ppm (8-hour)
    STEL: 125 ppm (15-minute)
    
    HANDLING PRECAUTIONS:
    - Use only in well-ventilated areas
    - Wear chemical-resistant gloves
    - Use respiratory protection when needed
    - Avoid skin and eye contact
    
    STORAGE:
    - Store in cool, dry place
    - Keep container tightly closed
    - Store away from heat sources
    """
    
    result = analyze_mail_and_pdf(email_body, pdf_text)
    
    print(f"Document Type: {result.get('doc_type', 'Unknown')}")
    print(f"Summary:\n{result.get('summary', 'No summary')}")
    print(f"Extracted Fields: {json.dumps(result.get('extracted_fields', {}), indent=2)}")
    
    return result

def test_calibration_certificate():
    """Test Calibration Certificate analysis."""
    print("\n" + "="*60)
    print("TEST 4: Calibration Certificate Analysis")
    print("="*60)
    
    email_body = """
    Hi Maintenance Team,
    
    The calibration certificate for our main pH meter is attached.
    Please check if the instrument is still within tolerance.
    
    If everything looks good, we can continue using it for another year.
    
    Thanks,
    Lab Manager
    """
    
    pdf_text = """
    CALIBRATION CERTIFICATE
    
    Instrument: pH Meter Model XYZ-100
    Serial Number: 12345
    Calibration Date: 2024-01-20
    Next Calibration Due: 2025-01-20
    
    CALIBRATION RESULTS:
    Test Point    Reference    Measured    Error    Tolerance    Status
    pH 4.00       4.00         4.02        +0.02    ±0.05       PASS
    pH 7.00       7.00         6.98        -0.02    ±0.05       PASS
    pH 10.00      10.00        10.04       +0.04    ±0.05       PASS
    
    Temperature Compensation: PASS
    Response Time: 15 seconds (Spec: <30 sec)
    
    Overall Result: INSTRUMENT APPROVED
    Calibrated by: Certified Calibration Lab
    Certificate Number: CAL-2024-0156
    """
    
    result = analyze_mail_and_pdf(email_body, pdf_text)
    
    print(f"Document Type: {result.get('doc_type', 'Unknown')}")
    print(f"Summary:\n{result.get('summary', 'No summary')}")
    print(f"Extracted Fields: {json.dumps(result.get('extracted_fields', {}), indent=2)}")
    
    return result

def test_email_with_specific_instructions():
    """Test email with specific instructions and links."""
    print("\n" + "="*60)
    print("TEST 5: Email with Specific Instructions")
    print("="*60)
    
    email_body = """
    Hi Team,
    
    Please review the attached invoice and process payment by Friday.
    
    Important: Before approving, please verify the amounts against our purchase order PO-2024-001.
    You can access the PO system at: https://po.company.com/orders/2024-001
    
    If you have any questions, please call me at ext. 1234.
    This is urgent as the supplier needs payment to ship our materials.
    
    Thanks,
    Procurement Manager
    """
    
    pdf_text = """
    INVOICE
    
    Invoice Number: INV-2024-0089
    Date: January 15, 2024
    Due Date: January 30, 2024
    
    Bill To:
    ABC Company
    123 Main Street
    City, State 12345
    
    Description                 Qty    Unit Price    Total
    Chemical Raw Material A     100kg    $25.00     $2,500.00
    Chemical Raw Material B     50kg     $40.00     $2,000.00
    Shipping                    1        $150.00    $150.00
    
    Subtotal:                                       $4,650.00
    Tax (8%):                                       $372.00
    Total Amount Due:                               $5,022.00
    
    Payment Terms: Net 15 days
    """
    
    result = analyze_mail_and_pdf(email_body, pdf_text)
    
    print(f"Document Type: {result.get('doc_type', 'Unknown')}")
    print(f"Summary:\n{result.get('summary', 'No summary')}")
    print(f"Extracted Fields: {json.dumps(result.get('extracted_fields', {}), indent=2)}")
    
    return result

def main():
    """Run all enhanced ChatGPT analysis tests."""
    print("Enhanced ChatGPT Analysis Test Suite")
    print("====================================")
    print("Testing improved email body interpretation and intelligent parameter analysis...")
    
    try:
        # Run all tests
        test_coa_with_good_parameters()
        test_coa_with_concerning_parameters()
        test_safety_data_sheet()
        test_calibration_certificate()
        test_email_with_specific_instructions()
        
        print("\n" + "="*60)
        print("ALL TESTS COMPLETED")
        print("="*60)
        print("Review the summaries above to verify:")
        print("1. Email body instructions are properly interpreted")
        print("2. Parameter analysis is performed for appropriate document types")
        print("3. Actionable guidance is provided based on analysis results")
        print("4. Critical information is highlighted appropriately")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        print("Make sure you have:")
        print("1. OPENAI_API_KEY set in your environment")
        print("2. Internet connection for OpenAI API calls")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
