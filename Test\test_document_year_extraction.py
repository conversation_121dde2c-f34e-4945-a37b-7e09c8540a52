#!/usr/bin/env python3
"""
Test script to debug document year extraction issues.

This test investigates why document_year was extracted as 2025 instead of 2018
for a CoA document with batch number 2018-11-08.
"""

import sys
import os

# Add the parent directory to the path so we can import core modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.interpreter.chatgpt_api import analyze_mail_and_pdf
from core.router import _extract_document_year, _parse_year_from_string

def test_year_extraction_logic():
    """Test the year extraction logic with various scenarios."""
    print("🔍 Testing Year Extraction Logic")
    print("=" * 50)
    
    # Test the parsing function directly
    test_cases = [
        ("2018-11-08", 2018),
        ("Not provided", None),
        ("2020-10-31", 2020),
        ("Batch: 2018-11-08", 2018),
        ("Manufacturing Date: Not provided", None),
        ("Expiry: 2020-10-31", 2020),
        ("Date: 2018", 2018),
        ("", None),
        ("No year here", None)
    ]
    
    print("Testing _parse_year_from_string function:")
    for test_input, expected in test_cases:
        result = _parse_year_from_string(test_input)
        status = "✅" if result == expected else "❌"
        print(f"{status} '{test_input}' -> {result} (expected: {expected})")
    
    print("\nTesting _extract_document_year function:")
    
    # Test with various extracted_fields scenarios
    scenarios = [
        {
            "name": "Good manufacturing date",
            "fields": {"manufacturing_date": "2018-11-08", "batch_number": "ABC123"},
            "expected": 2018
        },
        {
            "name": "Manufacturing date 'Not provided'",
            "fields": {"manufacturing_date": "Not provided", "batch_number": "2018-11-08"},
            "expected": 2018  # Now extracts from batch_number as fallback
        },
        {
            "name": "Only expiry date available",
            "fields": {"expiry_date": "2020-10-31", "batch_number": "ABC123"},
            "expected": 2020  # expiry_date now in the search list!
        },
        {
            "name": "Document date available",
            "fields": {"document_date": "2018-11-08", "batch_number": "ABC123"},
            "expected": 2018
        },
        {
            "name": "No date fields",
            "fields": {"batch_number": "ABC123", "company_name": "ACME"},
            "expected": 2025  # Falls back to current year
        }
    ]
    
    for scenario in scenarios:
        result = _extract_document_year(scenario["fields"], 2025)
        status = "✅" if result == scenario["expected"] else "❌"
        print(f"{status} {scenario['name']}: {result} (expected: {scenario['expected']})")
        if result != scenario["expected"]:
            print(f"   Fields: {scenario['fields']}")

def test_coa_extraction():
    """Test ChatGPT extraction with the problematic CoA."""
    print("\n" + "=" * 50)
    print("🧪 Testing CoA Extraction (Real Example)")
    print("=" * 50)
    
    email_body = """
    Hi Quality Team,
    
    Please review the attached CoA for batch 6092526.
    Contact me at +46-8-732 72 75 if you have any questions.
    
    Thanks,
    Quality Manager
    """
    
    pdf_text = """
    CERTIFICATE OF ANALYSIS
    Document Type: Certificate of Analysis for Acetic Acid 60% Food Grade
    Product: 6092526
    Batch Number: 2018-11-08
    Manufacturing Date: Not provided
    Expiry Date: 2020-10-31
    Company Name: Solveco AB
    
    Key Test Results:
    - Concentration: 60.1% (Specification: 60±1%)
    - Color: <5 Hazen units (Specification: ≤5)
    - Appearance: Approved (Specification: Clear liquid)
    
    All parameters meet specifications
    
    Contact Information:
    - Phone: +46-8-732 72 75
    - Fax: +46-8-732 72 76
    - Email: <EMAIL>
    """
    
    result = analyze_mail_and_pdf(email_body, pdf_text)
    
    print(f"📄 Document Type: {result.get('doc_type', 'Unknown')}")
    print(f"\n🔑 Extracted Fields:")
    extracted_fields = result.get('extracted_fields', {})
    for key, value in extracted_fields.items():
        print(f"  {key}: {value}")
    
    # Test what year would be extracted
    document_year = _extract_document_year(extracted_fields, 2025)
    print(f"\n📅 Document Year Extracted: {document_year}")
    
    # Analyze what went wrong
    print(f"\n🔍 Analysis:")
    
    # Check if batch number was extracted as a date field
    batch_as_date = False
    for field_name in ["signing_date", "signed_date", "document_date", "issue_date",
                       "manufacturing_date", "test_date", "analysis_date", "created_date",
                       "date", "year"]:
        if field_name in extracted_fields:
            value = extracted_fields[field_name]
            print(f"  Found date field '{field_name}': {value}")
            year = _parse_year_from_string(str(value))
            if year:
                print(f"    -> Parsed year: {year}")
            else:
                print(f"    -> Could not parse year from: {value}")
    
    # Check if expiry_date was extracted (now in search list)
    if "expiry_date" in extracted_fields:
        expiry = extracted_fields["expiry_date"]
        expiry_year = _parse_year_from_string(str(expiry))
        print(f"  Found expiry_date: {expiry} -> year: {expiry_year}")
        print(f"  ✅ NOTE: expiry_date is NOW in the search list for document_year!")
    
    # Check if batch_number contains a date
    if "batch_number" in extracted_fields:
        batch = extracted_fields["batch_number"]
        batch_year = _parse_year_from_string(str(batch))
        print(f"  Found batch_number: {batch} -> year: {batch_year}")
        print(f"  ⚠️ NOTE: batch_number is NOT in the search list for document_year!")
    
    return extracted_fields, document_year

def suggest_fixes():
    """Suggest fixes for the document year extraction issue."""
    print("\n" + "=" * 50)
    print("💡 Suggested Fixes")
    print("=" * 50)
    
    print("1. 🔧 Add more date fields to search list:")
    print("   - Add 'expiry_date' to the date_fields list")
    print("   - Add 'batch_date' to handle batch numbers with dates")
    print("   - Add 'creation_date' as alternative")
    
    print("\n2. 🤖 Improve ChatGPT extraction:")
    print("   - Ensure batch numbers with dates are extracted as date fields")
    print("   - Better handling of 'Not provided' dates")
    print("   - Extract document creation/issue dates more reliably")
    
    print("\n3. 📋 Fallback strategy:")
    print("   - Try to extract year from batch_number if no date fields found")
    print("   - Use expiry_date as last resort before current year")
    print("   - Add validation to warn when falling back to current year")

def main():
    """Run document year extraction debugging."""
    print("Document Year Extraction Debug")
    print("=============================")
    
    try:
        test_year_extraction_logic()
        extracted_fields, document_year = test_coa_extraction()
        suggest_fixes()
        
        print("\n" + "=" * 50)
        print("📊 SUMMARY")
        print("=" * 50)
        
        if document_year in [2018, 2020]:  # Either batch date or expiry date
            print(f"✅ Document year correctly extracted: {document_year}")
            print("   ✅ Fixed: Now extracts from expiry_date or batch_date")
        elif document_year == 2025:
            print("❌ ISSUE: Document year still defaulting to current year (2025)")
            print("   Expected: 2018 (from batch date) or 2020 (from expiry date)")
        else:
            print(f"⚠️ Unexpected document year: {document_year}")

        return document_year in [2018, 2020]
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        print("Make sure you have OPENAI_API_KEY set in your environment")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
