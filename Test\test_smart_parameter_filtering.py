#!/usr/bin/env python3
"""
Test script to verify smart parameter filtering for CoA documents.

This test demonstrates that the AI only reports concerning parameters
and summarizes the rest, making summaries much cleaner and more focused.
"""

import sys
import os

# Add the parent directory to the path so we can import core modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.interpreter.chatgpt_api import analyze_mail_and_pdf

def test_smart_parameter_filtering():
    """Test that only concerning parameters are reported in detail."""
    print("🔬 Testing Smart Parameter Filtering")
    print("=" * 60)
    
    email_body = """
    Hi Quality Team,
    
    Please review the attached CoA for batch 6092526.
    Contact me at +46-8-732 72 75 if you have any questions.
    
    Thanks,
    Quality Manager
    """
    
    # This is based on your real example with many parameters
    pdf_text = """
    CERTIFICATE OF ANALYSIS
    Document Type: Certificate of Analysis for Acetic Acid 60% Food Grade
    Product: 6092526
    Batch Number: 2018-11-08
    Manufacturing Date: Not provided
    Expiry Date: 2020-10-31
    Company Name: Solveco AB
    
    Key Test Results:
    - Concentration: 60.1% (Specification: 60±1%)
    - Color: <5 Hazen units (Specification: ≤5)
    - Appearance: Approved (Specification: Clear liquid)
    - Raw Material Acetic Acid:
      - Concentration: 99.90% (Specification: ≥99.85%)
      - Aldehydes: <0.001% (Specification: ≤0.050%)
      - Non-volatile substances: 0.001% (Specification: ≤0.003%)
      - Iron (Fe): <0.5 ppm (Specification: ≤0.5 ppm)
      - Chlorides (Cl): <1 ppm (Specification: ≤1 ppm)
      - Mercury (Hg): <1 ppm (Specification: ≤1 ppm)
      - Formic Acid: <0.02% (Specification: ≤0.050%)
      - Permanganate Time: >60 minutes (Specification: ≥60 minutes)
      - Sulfates (SO4): <1 ppm (Specification: ≤1 ppm)
      - Heavy Metals (Pb): <0.5 ppm (Specification: ≤0.5 ppm)
    
    Compliance Status: All parameters meet specifications
    Contact Information:
    - Phone: +46-8-732 72 75
    - Fax: +46-8-732 72 76
    - Email: <EMAIL>
    """
    
    result = analyze_mail_and_pdf(email_body, pdf_text)
    
    print(f"📄 Document Type: {result.get('doc_type', 'Unknown')}")
    print(f"\n📋 Summary:")
    print(result.get('summary', 'No summary'))
    
    # Analyze the summary for smart filtering
    summary = result.get('summary', '')
    
    print(f"\n🔍 Smart Filtering Analysis:")
    print("=" * 40)
    
    # Count how many individual parameters are listed
    parameter_count = 0
    lines = summary.split('\n')
    for line in lines:
        if 'ppm' in line.lower() or '%' in line or 'hazen' in line.lower():
            if 'specification' in line.lower():
                parameter_count += 1
    
    # Check for summary statements
    has_summary_statement = any([
        'all parameters' in summary.lower(),
        'all other parameters' in summary.lower(),
        'remaining parameters' in summary.lower()
    ])
    
    # Check for section formatting
    has_sections = '--- DOCUMENT INFO ---' in summary and '--- ACTION REQUIRED ---' in summary
    
    # Check for contact info
    has_contact_info = '+46-8-732 72 75' in summary
    
    # Check for no duplicate review statements
    review_count = summary.lower().count('please review the attachment')
    
    checks = [
        ("Uses section formatting", has_sections),
        ("Limited parameter details (≤3)", parameter_count <= 3),
        ("Has summary statement for other parameters", has_summary_statement),
        ("Includes contact information", has_contact_info),
        ("No duplicate review statements", review_count <= 1),
        ("Clean, readable structure", len(summary.split('\n')) < 20)
    ]
    
    all_passed = True
    for check_name, passed in checks:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} {check_name}")
        if not passed:
            all_passed = False
    
    print(f"\n📊 Parameter Details Found: {parameter_count}")
    print(f"📊 Review Statement Count: {review_count}")
    print(f"📊 Summary Length: {len(summary.split())} words")
    
    return all_passed

def test_concerning_parameters():
    """Test with some concerning parameters to see detailed reporting."""
    print("\n" + "=" * 60)
    print("⚠️ Testing with Concerning Parameters")
    print("=" * 60)
    
    email_body = """
    Hi Team,
    
    Please review this CoA urgently. Check the moisture levels carefully.
    Call me at ext. 555 if there are any issues.
    
    Production Manager
    """
    
    pdf_text = """
    CERTIFICATE OF ANALYSIS
    Batch: ABC-789
    Product: Chemical Powder
    
    TEST RESULTS:
    pH: 7.2 (Specification: 7.0-7.5) - OK
    Moisture: 0.95% (Specification: ≤1.0%) - BORDERLINE (close to limit)
    Purity: 99.7% (Specification: ≥99.5%) - OK
    Heavy Metals: 0.1 ppm (Specification: ≤0.5 ppm) - OK
    Particle Size: 150 μm (Specification: 100-200 μm) - OK
    Density: 1.2 g/cm³ (Specification: 1.1-1.3 g/cm³) - OK
    Ash Content: 0.05% (Specification: ≤0.1%) - OK
    Volatile Matter: 0.02% (Specification: ≤0.05%) - OK
    
    Overall: Approved with attention to moisture content
    """
    
    result = analyze_mail_and_pdf(email_body, pdf_text)
    
    print(f"📄 Document Type: {result.get('doc_type', 'Unknown')}")
    print(f"\n📋 Summary:")
    print(result.get('summary', 'No summary'))
    
    summary = result.get('summary', '')
    
    # Check if moisture (the concerning parameter) is highlighted
    moisture_highlighted = 'moisture' in summary.lower() and ('0.95%' in summary or 'borderline' in summary.lower())
    
    # Check if other parameters are summarized
    has_summary_for_others = 'other parameters' in summary.lower() or 'remaining parameters' in summary.lower()
    
    print(f"\n🔍 Concerning Parameter Analysis:")
    print("=" * 40)
    print(f"✅ Moisture highlighted: {moisture_highlighted}")
    print(f"✅ Other parameters summarized: {has_summary_for_others}")
    
    return moisture_highlighted and has_summary_for_others

def main():
    """Run smart parameter filtering tests."""
    print("Smart Parameter Filtering Test Suite")
    print("===================================")
    
    try:
        test1_passed = test_smart_parameter_filtering()
        test2_passed = test_concerning_parameters()
        
        print("\n" + "=" * 60)
        print("📊 FINAL RESULTS")
        print("=" * 60)
        
        if test1_passed and test2_passed:
            print("🎉 ALL TESTS PASSED!")
            print("✅ Smart parameter filtering is working correctly")
            print("✅ Only concerning parameters are shown in detail")
            print("✅ Clean section formatting is applied")
            print("✅ Contact information is properly captured")
            print("✅ No redundant review statements")
            return True
        else:
            print("⚠️ Some tests failed")
            print(f"All Parameters Test: {'PASS' if test1_passed else 'FAIL'}")
            print(f"Concerning Parameters Test: {'PASS' if test2_passed else 'FAIL'}")
            return False
            
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        print("Make sure you have OPENAI_API_KEY set in your environment")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
