#!/usr/bin/env python3
"""
Test script for intelligent document type analysis.

This script tests how the AI automatically determines which document types
need parameter analysis and which ones need different types of smart analysis.

The AI should intelligently decide what's most important for each document type.
"""

import sys
import os
import json

# Add the parent directory to the path so we can import core modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.interpreter.chatgpt_api import analyze_mail_and_pdf

def test_document_type(doc_name, email_body, pdf_text):
    """Helper function to test a document type and display results."""
    print(f"\n{'='*60}")
    print(f"TESTING: {doc_name}")
    print('='*60)
    
    result = analyze_mail_and_pdf(email_body, pdf_text)
    
    print(f"📄 Document Type Identified: {result.get('doc_type', 'Unknown')}")
    print(f"\n📋 Summary:")
    print(result.get('summary', 'No summary'))
    
    extracted = result.get('extracted_fields', {})
    if extracted:
        print(f"\n🔍 Key Extracted Data:")
        for key, value in extracted.items():
            if key != '_processing_info':  # Skip internal metadata
                print(f"  • {key}: {value}")
    
    return result

def test_quality_control_report():
    """Test Quality Control Report - should get parameter analysis."""
    email_body = "Please review the attached QC report for today's production batch."
    
    pdf_text = """
    QUALITY CONTROL REPORT
    Batch: QC-2024-001
    Date: January 20, 2024
    
    MEASUREMENTS:
    Temperature: 85°C (Target: 80-90°C) ✓
    Pressure: 2.1 bar (Target: 2.0-2.5 bar) ✓
    Flow Rate: 15.2 L/min (Target: 15-20 L/min) ✓
    Concentration: 94% (Target: ≥95%) ⚠️
    
    RECOMMENDATION: Concentration slightly below target. Monitor next batch.
    """
    
    return test_document_type("Quality Control Report", email_body, pdf_text)

def test_environmental_report():
    """Test Environmental Report - should get compliance analysis."""
    email_body = "Environmental monitoring results attached. Please check against regulatory limits."
    
    pdf_text = """
    ENVIRONMENTAL MONITORING REPORT
    Site: Manufacturing Plant A
    Period: January 2024
    
    EMISSIONS DATA:
    NOx: 45 mg/m³ (Limit: 50 mg/m³) ✓
    SO2: 12 mg/m³ (Limit: 20 mg/m³) ✓
    Particulates: 8 mg/m³ (Limit: 10 mg/m³) ✓
    CO: 35 mg/m³ (Limit: 30 mg/m³) ⚠️
    
    STATUS: One parameter exceeds limit. Corrective action required.
    """
    
    return test_document_type("Environmental Report", email_body, pdf_text)

def test_purchase_order():
    """Test Purchase Order - should focus on commercial details, not parameters."""
    email_body = "Please process the attached purchase order. Delivery needed by month end."
    
    pdf_text = """
    PURCHASE ORDER
    PO Number: PO-2024-0156
    Date: January 20, 2024
    Vendor: Chemical Supplies Inc.
    
    ITEMS:
    1. Sodium Hydroxide 50kg @ $2.50/kg = $125.00
    2. Hydrochloric Acid 25L @ $8.00/L = $200.00
    3. Safety Equipment Set @ $150.00 = $150.00
    
    Subtotal: $475.00
    Tax: $38.00
    Total: $513.00
    
    Delivery Required: January 31, 2024
    Payment Terms: Net 30
    """
    
    return test_document_type("Purchase Order", email_body, pdf_text)

def test_material_certificate():
    """Test Material Certificate - should get material property analysis."""
    email_body = "Material certificate for the steel we ordered. Please verify it meets our specs."
    
    pdf_text = """
    MATERIAL CERTIFICATE
    Material: Stainless Steel 316L
    Heat Number: H123456
    
    CHEMICAL COMPOSITION:
    Carbon: 0.025% (Max 0.030%) ✓
    Chromium: 17.2% (16.0-18.0%) ✓
    Nickel: 10.8% (10.0-14.0%) ✓
    Molybdenum: 2.1% (2.0-3.0%) ✓
    
    MECHANICAL PROPERTIES:
    Tensile Strength: 580 MPa (Min 515 MPa) ✓
    Yield Strength: 290 MPa (Min 205 MPa) ✓
    Elongation: 45% (Min 40%) ✓
    
    CERTIFICATION: Material meets ASTM A240 requirements.
    """
    
    return test_document_type("Material Certificate", email_body, pdf_text)

def test_shipping_document():
    """Test Shipping Document - should focus on logistics, not parameters."""
    email_body = "Shipping confirmation attached. Please track the delivery status."
    
    pdf_text = """
    SHIPPING CONFIRMATION
    Tracking Number: TRK123456789
    Ship Date: January 20, 2024
    Expected Delivery: January 22, 2024
    
    SHIPMENT DETAILS:
    From: Warehouse A, City X
    To: Plant B, City Y
    Carrier: Express Logistics
    Service: Next Day Air
    
    CONTENTS:
    - 5 boxes Chemical Samples
    - 2 boxes Laboratory Equipment
    - 1 box Documentation
    
    Total Weight: 45 kg
    Insurance: $5,000
    """
    
    return test_document_type("Shipping Document", email_body, pdf_text)

def test_inspection_report():
    """Test Inspection Report - should get compliance/defect analysis."""
    email_body = "Equipment inspection results. Please review for any safety concerns."
    
    pdf_text = """
    EQUIPMENT INSPECTION REPORT
    Equipment: Pressure Vessel PV-001
    Inspector: John Smith, PE
    Date: January 20, 2024
    
    INSPECTION FINDINGS:
    Visual Inspection: No visible defects ✓
    Pressure Test: 15 bar (Required: 12 bar) ✓
    Wall Thickness: 8.2mm (Min: 8.0mm) ✓
    Valve Operation: All valves functional ✓
    Safety Systems: Emergency shutdown tested ✓
    
    MINOR ISSUES:
    - Gauge calibration due next month
    - Paint touch-up needed on support legs
    
    OVERALL STATUS: APPROVED for continued operation
    Next Inspection: January 2025
    """
    
    return test_document_type("Inspection Report", email_body, pdf_text)

def main():
    """Run intelligent document analysis tests."""
    print("Intelligent Document Analysis Test Suite")
    print("=======================================")
    print("Testing how AI decides which documents need parameter analysis...")
    
    try:
        # Test documents that SHOULD get parameter analysis
        print("\n🔬 DOCUMENTS THAT SHOULD GET PARAMETER ANALYSIS:")
        test_quality_control_report()
        test_environmental_report()
        test_material_certificate()
        test_inspection_report()
        
        # Test documents that should focus on OTHER important aspects
        print("\n📋 DOCUMENTS THAT SHOULD FOCUS ON OTHER ASPECTS:")
        test_purchase_order()
        test_shipping_document()
        
        print("\n" + "="*60)
        print("ANALYSIS COMPLETE")
        print("="*60)
        print("Review the summaries above to verify:")
        print("✓ Parameter analysis applied to technical/quality documents")
        print("✓ Commercial documents focus on financial/delivery aspects")
        print("✓ Logistics documents focus on shipping/tracking details")
        print("✓ AI provides appropriate actionable guidance for each type")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        print("Make sure you have OPENAI_API_KEY set in your environment")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
