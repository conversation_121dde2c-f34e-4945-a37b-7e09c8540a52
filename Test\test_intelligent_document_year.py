#!/usr/bin/env python3
"""
Test script to verify ChatGPT's intelligent document year extraction.

This test verifies that ChatGPT can determine the most contextually relevant
year for different types of documents.
"""

import sys
import os

# Add the parent directory to the path so we can import core modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.interpreter.chatgpt_api import analyze_mail_and_pdf
from core.router import _extract_document_year

def test_coa_with_manufacturing_year():
    """Test CoA where manufacturing year (2018) should be the document year."""
    print("🧪 Test 1: CoA with Manufacturing Year (Tillverkningsdatum)")
    print("=" * 60)
    
    email_body = """
    Hi Quality Team,
    
    Please review the attached CoA for batch 6092526.
    Contact me if you have any questions.
    
    Quality Manager
    """
    
    pdf_text = """
    CERTIFICATE OF ANALYSIS
    Document Type: Certificate of Analysis for Acetic Acid 60% Food Grade
    Product: 6092526
    Batch Number: 2018-11-08
    Tillverkningsdatum (Manufacturing Date): 2018-11-08
    Expiry Date: 2020-10-31
    Company Name: Solveco AB
    
    Test Results:
    - Concentration: 60.1% (Specification: 60±1%)
    - All parameters meet specifications
    
    Analysis performed: 2019-01-15
    Certificate issued: 2019-01-16
    """
    
    result = analyze_mail_and_pdf(email_body, pdf_text)
    
    print(f"📄 Document Type: {result.get('doc_type', 'Unknown')}")
    print(f"🔑 Extracted Fields:")
    extracted_fields = result.get('extracted_fields', {})
    for key, value in extracted_fields.items():
        print(f"  {key}: {value}")
    
    document_year = _extract_document_year(extracted_fields, 2025)
    print(f"\n📅 Final Document Year: {document_year}")
    
    # For a CoA, the manufacturing year (2018) should be most relevant
    expected_year = 2018
    success = document_year == expected_year
    status = "✅ PASS" if success else "❌ FAIL"
    print(f"{status} Expected manufacturing year {expected_year}, got {document_year}")
    
    return success, document_year

def test_invoice_with_creation_year():
    """Test invoice where creation year should be the document year."""
    print("\n🧪 Test 2: Invoice with Creation Year")
    print("=" * 60)
    
    email_body = """
    Hi Accounts,
    
    Please process the attached invoice.
    
    Procurement
    """
    
    pdf_text = """
    INVOICE #INV-2024-789
    Date: January 20, 2024
    Amount: $5,250.00
    
    For services rendered in December 2023
    Payment due: February 19, 2024
    
    Items delivered: November 2023
    """
    
    result = analyze_mail_and_pdf(email_body, pdf_text)
    
    print(f"📄 Document Type: {result.get('doc_type', 'Unknown')}")
    print(f"🔑 Extracted Fields:")
    extracted_fields = result.get('extracted_fields', {})
    for key, value in extracted_fields.items():
        print(f"  {key}: {value}")
    
    document_year = _extract_document_year(extracted_fields, 2025)
    print(f"\n📅 Final Document Year: {document_year}")
    
    # For an invoice, the invoice creation year (2024) should be most relevant
    expected_year = 2024
    success = document_year == expected_year
    status = "✅ PASS" if success else "❌ FAIL"
    print(f"{status} Expected invoice year {expected_year}, got {document_year}")
    
    return success, document_year

def test_calibration_certificate():
    """Test calibration certificate where calibration year should be the document year."""
    print("\n🧪 Test 3: Calibration Certificate")
    print("=" * 60)
    
    email_body = """
    Hi Team,
    
    Calibration certificate for pH meter is attached.
    
    Lab Manager
    """
    
    pdf_text = """
    CALIBRATION CERTIFICATE
    
    Instrument: pH Meter Model XYZ
    Serial Number: PM-2019-456
    
    Calibration Date: March 15, 2023
    Certificate Valid Until: March 15, 2024
    
    Calibration performed by: Certified Lab
    Certificate issued: March 16, 2023
    
    Instrument manufactured: 2019
    Previous calibration: March 2022
    """
    
    result = analyze_mail_and_pdf(email_body, pdf_text)
    
    print(f"📄 Document Type: {result.get('doc_type', 'Unknown')}")
    print(f"🔑 Extracted Fields:")
    extracted_fields = result.get('extracted_fields', {})
    for key, value in extracted_fields.items():
        print(f"  {key}: {value}")
    
    document_year = _extract_document_year(extracted_fields, 2025)
    print(f"\n📅 Final Document Year: {document_year}")
    
    # For a calibration certificate, the calibration year (2023) should be most relevant
    expected_year = 2023
    success = document_year == expected_year
    status = "✅ PASS" if success else "❌ FAIL"
    print(f"{status} Expected calibration year {expected_year}, got {document_year}")
    
    return success, document_year

def main():
    """Run intelligent document year extraction tests."""
    print("Intelligent Document Year Extraction Test")
    print("========================================")
    
    try:
        test1_success, year1 = test_coa_with_manufacturing_year()
        test2_success, year2 = test_invoice_with_creation_year()
        test3_success, year3 = test_calibration_certificate()
        
        print("\n" + "=" * 60)
        print("📊 FINAL RESULTS")
        print("=" * 60)
        
        results = [
            ("CoA Manufacturing Year", test1_success, year1, 2018),
            ("Invoice Creation Year", test2_success, year2, 2024),
            ("Calibration Year", test3_success, year3, 2023)
        ]
        
        all_passed = True
        for test_name, success, actual, expected in results:
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"{status} {test_name}: {actual} (expected: {expected})")
            if not success:
                all_passed = False
        
        if all_passed:
            print("\n🎉 ALL TESTS PASSED!")
            print("✅ ChatGPT successfully determines contextually relevant document years")
            print("✅ Manufacturing years for products/chemicals")
            print("✅ Creation years for commercial documents")
            print("✅ Service/calibration years for certificates")
        else:
            print("\n⚠️ Some tests failed")
            print("💡 ChatGPT may need more specific instructions for document year extraction")
        
        return all_passed
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        print("Make sure you have OPENAI_API_KEY set in your environment")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
